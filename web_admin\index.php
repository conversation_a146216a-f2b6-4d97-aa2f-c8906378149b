<?php
session_start();
require_once 'config.php';

// Cek login
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Ambil statistik
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total_books FROM books");
    $total_books = $stmt->fetch()['total_books'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'petugas'");
    $total_users = $stmt->fetch()['total_users'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total_borrowed FROM borrow_records WHERE status = 'dipinjam'");
    $total_borrowed = $stmt->fetch()['total_borrowed'];
    
    $stmt = $pdo->query("SELECT SUM(stock) as total_stock FROM books");
    $total_stock = $stmt->fetch()['total_stock'];
} catch (PDOException $e) {
    $total_books = $total_users = $total_borrowed = $total_stock = 0;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - Perpustakaan Digital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.2rem 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .card-stats {
            border-left: 4px solid;
            transition: transform 0.2s;
        }
        .card-stats:hover {
            transform: translateY(-5px);
        }
        .card-primary { border-left-color: #007bff; }
        .card-success { border-left-color: #28a745; }
        .card-warning { border-left-color: #ffc107; }
        .card-info { border-left-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3 text-white">
                        <h4><i class="fas fa-book"></i> Perpustakaan</h4>
                        <small>Admin Panel</small>
                    </div>
                    <nav class="nav flex-column px-3">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link" href="books.php">
                            <i class="fas fa-book"></i> Kelola Buku
                        </a>
                        <a class="nav-link" href="add_book.php">
                            <i class="fas fa-plus"></i> Tambah Buku
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users"></i> Kelola User
                        </a>
                        <a class="nav-link" href="borrows.php">
                            <i class="fas fa-exchange-alt"></i> Peminjaman
                        </a>
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Dashboard</h2>
                        <div class="text-muted">
                            Selamat datang, <?= htmlspecialchars($_SESSION['admin_name']) ?>
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card card-stats card-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5 class="card-title text-muted">Total Buku</h5>
                                            <h2 class="text-primary"><?= number_format($total_books) ?></h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-book fa-2x text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card card-stats card-success">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5 class="card-title text-muted">Total Stok</h5>
                                            <h2 class="text-success"><?= number_format($total_stock) ?></h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-boxes fa-2x text-success"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card card-stats card-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5 class="card-title text-muted">Dipinjam</h5>
                                            <h2 class="text-warning"><?= number_format($total_borrowed) ?></h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-hand-holding fa-2x text-warning"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card card-stats card-info">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h5 class="card-title text-muted">Total User</h5>
                                            <h2 class="text-info"><?= number_format($total_users) ?></h2>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x text-info"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-plus"></i> Aksi Cepat</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="add_book.php" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Tambah Buku Baru
                                        </a>
                                        <a href="books.php" class="btn btn-outline-primary">
                                            <i class="fas fa-list"></i> Lihat Semua Buku
                                        </a>
                                        <a href="borrows.php" class="btn btn-outline-warning">
                                            <i class="fas fa-exchange-alt"></i> Kelola Peminjaman
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle"></i> Informasi Sistem</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Versi:</strong> 1.0.0</p>
                                    <p><strong>Database:</strong> MySQL</p>
                                    <p><strong>Server:</strong> <?= $_SERVER['SERVER_NAME'] ?></p>
                                    <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
