<?php
require_once 'includes/header.php';

// Filter status
$status_filter = isset($_GET['status']) ? clean($_GET['status']) : '';

// Query dasar
$sql = "SELECT p.*, a.nama_lengkap, b.judul 
        FROM peminjaman p
        JOIN anggota a ON p.id_anggota = a.id_anggota
        JOIN buku b ON p.id_buku = b.id_buku";

// Tambahkan filter jika ada
if (!empty($status_filter)) {
    $sql .= " WHERE p.status_peminjaman = '$status_filter'";
}

// Tambahkan pengurutan
$sql .= " ORDER BY p.created_at DESC";

$result = query($sql);
$peminjaman = fetch_all($result);

// Hitung jumlah per status
$count_menunggu = count_loans('menunggu');
$count_dipinjam = count_loans('dipinjam');
$count_dikembalikan = count_loans('dikembalikan');
$count_terlambat = count_loans('terlambat');
$count_ditolak = count_loans('ditolak');
$count_all = count_loans();
?>

<!-- Peminjaman Content -->
<div class="container-fluid">
    <!-- Filter Status -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h6 class="mb-3 mb-md-0">Filter Status:</h6>
                <div class="d-flex flex-wrap gap-2">
                    <a href="peminjaman.php" class="btn <?php echo empty($status_filter) ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Semua (<?php echo $count_all; ?>)
                    </a>
                    <a href="peminjaman.php?status=menunggu" class="btn <?php echo $status_filter == 'menunggu' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                        Menunggu (<?php echo $count_menunggu; ?>)
                    </a>
                    <a href="peminjaman.php?status=dipinjam" class="btn <?php echo $status_filter == 'dipinjam' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Dipinjam (<?php echo $count_dipinjam; ?>)
                    </a>
                    <a href="peminjaman.php?status=dikembalikan" class="btn <?php echo $status_filter == 'dikembalikan' ? 'btn-success' : 'btn-outline-success'; ?>">
                        Dikembalikan (<?php echo $count_dikembalikan; ?>)
                    </a>
                    <a href="peminjaman.php?status=terlambat" class="btn <?php echo $status_filter == 'terlambat' ? 'btn-danger' : 'btn-outline-danger'; ?>">
                        Terlambat (<?php echo $count_terlambat; ?>)
                    </a>
                    <a href="peminjaman.php?status=ditolak" class="btn <?php echo $status_filter == 'ditolak' ? 'btn-secondary' : 'btn-outline-secondary'; ?>">
                        Ditolak (<?php echo $count_ditolak; ?>)
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Daftar Peminjaman -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                Daftar Peminjaman 
                <?php if (!empty($status_filter)): ?>
                    - Status: <span class="badge bg-<?php 
                        switch ($status_filter) {
                            case 'menunggu': echo 'warning'; break;
                            case 'dipinjam': echo 'primary'; break;
                            case 'dikembalikan': echo 'success'; break;
                            case 'terlambat': echo 'danger'; break;
                            case 'ditolak': echo 'secondary'; break;
                        }
                    ?>"><?php echo $status_filter; ?></span>
                <?php endif; ?>
            </h6>
            <a href="tambah_peminjaman.php" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Tambah Peminjaman
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Anggota</th>
                            <th>Buku</th>
                            <th>Tanggal Pinjam</th>
                            <th>Tanggal Kembali</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if (count($peminjaman) > 0) {
                            foreach ($peminjaman as $p) {
                                // Set warna status
                                $status_class = '';
                                switch ($p['status_peminjaman']) {
                                    case 'menunggu':
                                        $status_class = 'warning';
                                        break;
                                    case 'dipinjam':
                                        $status_class = 'primary';
                                        break;
                                    case 'dikembalikan':
                                        $status_class = 'success';
                                        break;
                                    case 'terlambat':
                                        $status_class = 'danger';
                                        break;
                                    case 'ditolak':
                                        $status_class = 'secondary';
                                        break;
                                }
                                
                                echo "<tr>
                                        <td>{$p['id_peminjaman']}</td>
                                        <td>{$p['nama_lengkap']}</td>
                                        <td>{$p['judul']}</td>
                                        <td>" . format_tanggal($p['tanggal_pinjam']) . "</td>
                                        <td>" . format_tanggal($p['tanggal_kembali']) . "</td>
                                        <td><span class='badge bg-{$status_class}'>{$p['status_peminjaman']}</span></td>
                                        <td>
                                            <a href='detail_peminjaman.php?id={$p['id_peminjaman']}' class='btn btn-sm btn-info'>
                                                <i class='fas fa-eye'></i>
                                            </a>";
                                
                                // Tombol aksi berdasarkan status
                                if ($p['status_peminjaman'] == 'menunggu') {
                                    echo "<a href='proses_peminjaman.php?id={$p['id_peminjaman']}&action=approve' class='btn btn-sm btn-success'>
                                            <i class='fas fa-check'></i>
                                          </a>
                                          <a href='proses_peminjaman.php?id={$p['id_peminjaman']}&action=reject' class='btn btn-sm btn-danger'>
                                            <i class='fas fa-times'></i>
                                          </a>";
                                } elseif ($p['status_peminjaman'] == 'dipinjam') {
                                    echo "<a href='proses_peminjaman.php?id={$p['id_peminjaman']}&action=return' class='btn btn-sm btn-success'>
                                            <i class='fas fa-undo'></i>
                                          </a>";
                                }
                                
                                echo "</td>
                                    </tr>";
                            }
                        } else {
                            echo "<tr><td colspan='7' class='text-center'>Tidak ada data peminjaman</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
