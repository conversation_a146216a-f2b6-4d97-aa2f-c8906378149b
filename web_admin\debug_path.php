<?php
echo "<h2>Debug Path Upload</h2>";

// Cek current directory
echo "<h3>Current Directory:</h3>";
echo "<p>" . getcwd() . "</p>";

// Cek parent directory
echo "<h3>Parent Directory:</h3>";
echo "<p>" . dirname(getcwd()) . "</p>";

// Test berbagai path
$test_paths = [
    "uploads/",
    "../uploads/",
    "../api/uploads/",
    "../../api/uploads/",
    "../perpus_andro/api/uploads/",
    "../../perpus_andro/api/uploads/"
];

echo "<h3>Test Path Existence:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Path</th><th>Exists</th><th>Writable</th><th>Real Path</th></tr>";

foreach ($test_paths as $path) {
    $exists = file_exists($path) ? "YES" : "NO";
    $writable = is_writable($path) ? "YES" : "NO";
    $realpath = realpath($path) ?: "Not found";
    
    echo "<tr>";
    echo "<td>$path</td>";
    echo "<td style='color: " . ($exists == "YES" ? "green" : "red") . "'>$exists</td>";
    echo "<td style='color: " . ($writable == "YES" ? "green" : "red") . "'>$writable</td>";
    echo "<td>$realpath</td>";
    echo "</tr>";
}
echo "</table>";

// Cek struktur direktori
echo "<h3>Directory Structure:</h3>";
function listDirectory($dir, $level = 0) {
    if (!is_dir($dir)) return;
    
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;
        
        $indent = str_repeat("&nbsp;&nbsp;&nbsp;&nbsp;", $level);
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            echo $indent . "📁 " . $file . "<br>";
            if ($level < 2) { // Limit depth
                listDirectory($path, $level + 1);
            }
        } else {
            echo $indent . "📄 " . $file . "<br>";
        }
    }
}

echo "<h4>From current directory (web_admin):</h4>";
listDirectory(".");

echo "<h4>From parent directory:</h4>";
listDirectory("..");

// Test create directory
echo "<h3>Test Create Directory:</h3>";
$test_create_paths = [
    "uploads/",
    "../api/uploads/",
    "../uploads/"
];

foreach ($test_create_paths as $path) {
    if (!file_exists($path)) {
        $created = mkdir($path, 0777, true);
        echo "<p>Create $path: " . ($created ? "SUCCESS" : "FAILED") . "</p>";
    } else {
        echo "<p>$path already exists</p>";
    }
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>
