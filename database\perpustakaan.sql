-- Database: per<PERSON><PERSON><PERSON>an
CREATE DATABASE IF NOT EXISTS perpustakaan;
USE perpustakaan;

-- Tabel users
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'petugas') NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel categories
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel books
CREATE TABLE books (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    author VARCHA<PERSON>(100) NOT NULL,
    publisher <PERSON><PERSON><PERSON><PERSON>(100),
    year VARCHAR(4),
    category_id INT,
    description TEXT,
    stock INT DEFAULT 0,
    image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Tabel borrow_records
CREATE TABLE borrow_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    book_id INT,
    borrow_date DATE NOT NULL,
    return_date DATE,
    due_date DATE NOT NULL,
    status ENUM('borrowed', 'returned', 'overdue') DEFAULT 'borrowed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (book_id) REFERENCES books(id)
);

-- Insert default admin user
INSERT INTO users (username, password, role, name) VALUES 
('admin', MD5('admin123'), 'admin', 'Administrator'),
('petugas', MD5('petugas123'), 'petugas', 'Petugas Perpustakaan');

-- Insert default categories
INSERT INTO categories (name, description) VALUES 
('Teknologi', 'Buku-buku tentang teknologi dan komputer'),
('Fiksi', 'Novel dan cerita fiksi'),
('Non-Fiksi', 'Buku-buku non-fiksi dan referensi'),
('Sejarah', 'Buku-buku sejarah'),
('Sains', 'Buku-buku sains dan penelitian');

-- Insert sample books
INSERT INTO books (title, author, publisher, year, category_id, description, stock, image) VALUES 
('Pemrograman Android', 'John Doe', 'Informatika', '2023', 1, 'Buku panduan lengkap pemrograman Android untuk pemula hingga mahir.', 5, 'android_book.jpg'),
('Sejarah Indonesia', 'Jane Smith', 'Gramedia', '2022', 4, 'Sejarah lengkap Indonesia dari masa kerajaan hingga modern.', 3, 'sejarah_book.jpg'),
('Novel Laskar Pelangi', 'Andrea Hirata', 'Bentang Pustaka', '2021', 2, 'Novel inspiratif tentang perjuangan anak-anak Belitung menggapai mimpi.', 7, 'laskar_pelangi.jpg');
