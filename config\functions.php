<?php
/**
 * File fungsi-fungsi umum
 */

// Memuat file koneksi database
require_once 'database.php';

// Fungsi untuk mengecek apakah admin sudah login
function is_admin_logged_in() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

// Fungsi untuk redirect
function redirect($url) {
    // Cek apakah header sudah dikirim
    if (!headers_sent()) {
        header("Location: $url");
    } else {
        // Jika header sudah dikirim, gunakan JavaScript
        echo "<script>window.location.href='$url';</script>";
        // Fallback jika JavaScript dinonaktifkan
        echo "<noscript><meta http-equiv='refresh' content='0;url=$url'></noscript>";
    }
    exit();
}

// Fungsi untuk menampilkan pesan alert
function set_flash_message($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

// Fungsi untuk mendapatkan pesan flash
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $flash_message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $flash_message;
    }
    return null;
}

// Fungsi untuk menampilkan pesan flash
function display_flash_message() {
    $flash_message = get_flash_message();
    if ($flash_message) {
        $type = $flash_message['type'];
        $message = $flash_message['message'];

        echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
              </div>";
    }
}

// Fungsi untuk mengecek login admin
function admin_login($username, $password) {
    $username = clean($username);

    $sql = "SELECT * FROM admin WHERE username = '$username'";
    $result = query($sql);

    if (num_rows($result) === 1) {
        $admin = fetch_assoc($result);

        // Verifikasi password
        if (password_verify($password, $admin['password'])) {
            // Set session
            $_SESSION['admin_id'] = $admin['id_admin'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_nama'] = $admin['nama_admin'];
            $_SESSION['admin_role'] = $admin['username'] === 'admin' ? 'admin' : 'petugas';

            return true;
        }
    }

    return false;
}

// Fungsi untuk logout admin
function admin_logout() {
    // Hapus semua session
    session_unset();
    session_destroy();

    // Redirect ke halaman login
    redirect('../admin/login.php');
}

// Fungsi untuk mendapatkan data admin yang sedang login
function get_current_admin() {
    if (!is_admin_logged_in()) {
        return null;
    }

    $admin_id = $_SESSION['admin_id'];
    $sql = "SELECT * FROM admin WHERE id_admin = $admin_id";
    $result = query($sql);

    if (num_rows($result) === 1) {
        $admin = fetch_assoc($result);
        // Tambahkan role ke data admin
        $admin['role'] = $_SESSION['admin_role'] ?? ($admin['username'] === 'admin' ? 'admin' : 'petugas');
        return $admin;
    }

    return null;
}

// Fungsi untuk menghitung jumlah buku
function count_books() {
    $sql = "SELECT COUNT(*) as total FROM buku";
    $result = query($sql);
    $data = fetch_assoc($result);
    return $data['total'];
}

// Fungsi untuk menghitung jumlah anggota
function count_members() {
    $sql = "SELECT COUNT(*) as total FROM anggota";
    $result = query($sql);
    $data = fetch_assoc($result);
    return $data['total'];
}

// Fungsi untuk menghitung jumlah peminjaman
function count_loans($status = null) {
    $sql = "SELECT COUNT(*) as total FROM peminjaman";

    if ($status) {
        $sql .= " WHERE status_peminjaman = '$status'";
    }

    $result = query($sql);
    $data = fetch_assoc($result);
    return $data['total'];
}

/**
 * Fungsi untuk mendapatkan statistik buku berdasarkan kategori
 */
function get_books_by_category() {
    $sql = "SELECT k.nama_kategori, COUNT(b.id_buku) as jumlah_buku
            FROM kategori k
            LEFT JOIN buku b ON k.id_kategori = b.id_kategori
            GROUP BY k.id_kategori
            ORDER BY jumlah_buku DESC";
    $result = query($sql);
    return fetch_all($result);
}

/**
 * Fungsi untuk mendapatkan statistik peminjaman per bulan
 */
function get_monthly_loans($year = null) {
    if ($year === null) {
        $year = date('Y');
    }

    $months = array();
    $loans = array();

    for ($i = 1; $i <= 12; $i++) {
        $month_name = date('F', mktime(0, 0, 0, $i, 1));
        $months[] = $month_name;

        $sql = "SELECT COUNT(*) as total FROM peminjaman
                WHERE MONTH(tanggal_pinjam) = $i
                AND YEAR(tanggal_pinjam) = $year";
        $result = query($sql);
        $row = fetch_assoc($result);
        $loans[] = (int)$row['total'];
    }

    return array(
        'months' => $months,
        'loans' => $loans
    );
}

/**
 * Fungsi untuk mendapatkan statistik peminjaman berdasarkan status
 */
function get_loans_by_status() {
    $statuses = array('menunggu', 'dipinjam', 'dikembalikan', 'terlambat', 'ditolak');
    $result = array();

    foreach ($statuses as $status) {
        $sql = "SELECT COUNT(*) as total FROM peminjaman WHERE status_peminjaman = '$status'";
        $query_result = query($sql);
        $row = fetch_assoc($query_result);
        $result[$status] = (int)$row['total'];
    }

    return $result;
}

/**
 * Fungsi untuk mendapatkan buku paling populer (paling banyak dipinjam)
 */
function get_popular_books($limit = 5) {
    $sql = "SELECT b.id_buku, b.judul, COUNT(p.id_peminjaman) as total_peminjaman
            FROM buku b
            LEFT JOIN peminjaman p ON b.id_buku = p.id_buku
            GROUP BY b.id_buku
            ORDER BY total_peminjaman DESC
            LIMIT $limit";
    $result = query($sql);
    return fetch_all($result);
}

/**
 * Fungsi untuk mendapatkan anggota paling aktif (paling banyak meminjam)
 */
function get_active_members($limit = 5) {
    $sql = "SELECT a.id_anggota, a.nama_lengkap, COUNT(p.id_peminjaman) as total_peminjaman
            FROM anggota a
            LEFT JOIN peminjaman p ON a.id_anggota = p.id_anggota
            GROUP BY a.id_anggota
            ORDER BY total_peminjaman DESC
            LIMIT $limit";
    $result = query($sql);
    return fetch_all($result);
}

// Format tanggal ke format Indonesia
function format_tanggal($tanggal) {
    $bulan = [
        1 => 'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    ];

    $pecahkan = explode('-', $tanggal);

    return $pecahkan[2] . ' ' . $bulan[(int)$pecahkan[1]] . ' ' . $pecahkan[0];
}
