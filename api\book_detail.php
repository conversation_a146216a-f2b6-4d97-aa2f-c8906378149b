<?php
// Header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Memuat file koneksi database
require_once '../config/database.php';

// Ambil ID buku dari parameter
$id_buku = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validasi input
if ($id_buku <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID buku tidak valid'
    ]);
    exit;
}

// Query untuk mendapatkan detail buku
$sql = "SELECT b.*, k.nama_kategori
        FROM buku b
        LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
        WHERE b.id_buku = $id_buku";
$result = query($sql);

if (num_rows($result) === 1) {
    $book = fetch_assoc($result);

    echo json_encode([
        'success' => true,
        'message' => 'Detail buku berhasil diambil',
        'data' => $book
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Buku tidak ditemukan'
    ]);
}
?>
