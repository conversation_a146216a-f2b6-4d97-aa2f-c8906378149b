<?php
// Helper function untuk upload gambar ke multiple folder
function uploadImageToMultipleFolders($file, $filename = null) {
    $uploadDirs = [
        '../uploads/',           // Folder utama
        '../api/uploads/',       // Folder API
        '../web/uploads/',       // Folder web admin
        'uploads/'               // Folder lokal
    ];
    
    // Generate filename jika tidak disediakan
    if ($filename === null) {
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '_' . time() . '.' . $extension;
    }
    
    $uploadSuccess = true;
    $uploadedPaths = [];
    
    // Buat semua direktori jika belum ada
    foreach ($uploadDirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
        }
    }
    
    // Upload ke direktori pertama
    $mainPath = $uploadDirs[0] . $filename;
    if (move_uploaded_file($file['tmp_name'], $mainPath)) {
        $uploadedPaths[] = $mainPath;
        
        // Copy ke direktori lainnya
        foreach (array_slice($uploadDirs, 1) as $dir) {
            $targetPath = $dir . $filename;
            if (copy($mainPath, $targetPath)) {
                $uploadedPaths[] = $targetPath;
            } else {
                error_log("Failed to copy file to: " . $targetPath);
            }
        }
    } else {
        $uploadSuccess = false;
    }
    
    return [
        'success' => $uploadSuccess,
        'filename' => $filename,
        'paths' => $uploadedPaths
    ];
}

// Function untuk menghapus gambar dari semua folder
function deleteImageFromAllFolders($filename) {
    $uploadDirs = [
        '../uploads/',
        '../api/uploads/',
        '../web/uploads/',
        'uploads/'
    ];
    
    foreach ($uploadDirs as $dir) {
        $filePath = $dir . $filename;
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }
}

// Function untuk validasi gambar
function validateImage($file) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
    $maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowedTypes)) {
        return ['valid' => false, 'message' => 'Format file tidak diizinkan. Gunakan JPG, PNG, atau GIF.'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['valid' => false, 'message' => 'Ukuran file terlalu besar. Maksimal 5MB.'];
    }
    
    return ['valid' => true, 'message' => 'File valid'];
}

// Function untuk resize gambar
function resizeImage($sourcePath, $targetPath, $maxWidth = 800, $maxHeight = 600) {
    $imageInfo = getimagesize($sourcePath);
    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $sourceType = $imageInfo[2];
    
    // Hitung dimensi baru
    $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
    $newWidth = $sourceWidth * $ratio;
    $newHeight = $sourceHeight * $ratio;
    
    // Buat image resource berdasarkan type
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }
    
    // Buat image baru dengan dimensi yang sudah dihitung
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency untuk PNG dan GIF
    if ($sourceType == IMAGETYPE_PNG || $sourceType == IMAGETYPE_GIF) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Resize image
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
    
    // Save image berdasarkan type
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            imagejpeg($newImage, $targetPath, 90);
            break;
        case IMAGETYPE_PNG:
            imagepng($newImage, $targetPath);
            break;
        case IMAGETYPE_GIF:
            imagegif($newImage, $targetPath);
            break;
    }
    
    // Cleanup
    imagedestroy($sourceImage);
    imagedestroy($newImage);
    
    return true;
}
?>
