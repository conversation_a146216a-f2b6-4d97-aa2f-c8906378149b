<?php
session_start();
require_once '../config/functions.php';

// <PERSON>ka sudah login, redirect ke dashboard
if (is_admin_logged_in()) {
    redirect('index.php');
}

// Cek apakah sudah ada admin
$sql_check = "SELECT COUNT(*) as total FROM admin";
$result_check = query($sql_check);
$data_check = fetch_assoc($result_check);

// Jika belum ada admin, tambahkan admin default
if ($data_check['total'] == 0) {
    // Tambahkan admin default
    $admin_username = 'admin';
    $admin_password = 'admin123';
    $admin_name = 'Administrator';
    $admin_email = '<EMAIL>';

    // Hash password
    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

    // Tambah admin baru
    $sql_insert = "INSERT INTO admin (username, password, nama_admin, email)
                  VALUES ('$admin_username', '$hashed_password', '$admin_name', '$admin_email')";

    query($sql_insert);

    // Tambahkan petugas default
    $petugas_username = 'petugas';
    $petugas_password = 'petugas123';
    $petugas_name = 'Petugas Perpustakaan';
    $petugas_email = '<EMAIL>';

    // Hash password
    $hashed_password_petugas = password_hash($petugas_password, PASSWORD_DEFAULT);

    // Tambah petugas baru
    $sql_insert_petugas = "INSERT INTO admin (username, password, nama_admin, email)
                          VALUES ('$petugas_username', '$hashed_password_petugas', '$petugas_name', '$petugas_email')";

    query($sql_insert_petugas);
}

// Proses login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        set_flash_message('danger', 'Username dan password harus diisi!');
    } else {
        // Coba login normal
        if (admin_login($username, $password)) {
            redirect('index.php');
        }
        // Coba login dengan admin123 untuk admin
        else if ($username === 'admin' && $password === 'admin123') {
            // Ambil data admin
            $sql = "SELECT * FROM admin WHERE username = 'admin'";
            $result = query($sql);

            if (num_rows($result) === 1) {
                $admin = fetch_assoc($result);

                // Update password hash
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $sql_update = "UPDATE admin SET password = '$hashed_password' WHERE username = 'admin'";
                query($sql_update);

                // Set session
                $_SESSION['admin_id'] = $admin['id_admin'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_nama'] = $admin['nama_admin'];
                $_SESSION['admin_role'] = 'admin';

                redirect('index.php');
            }
        }
        // Coba login dengan petugas123 untuk petugas
        else if ($username === 'petugas' && $password === 'petugas123') {
            // Ambil data petugas
            $sql = "SELECT * FROM admin WHERE username = 'petugas'";
            $result = query($sql);

            if (num_rows($result) === 1) {
                $petugas = fetch_assoc($result);

                // Update password hash
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $sql_update = "UPDATE admin SET password = '$hashed_password' WHERE username = 'petugas'";
                query($sql_update);

                // Set session
                $_SESSION['admin_id'] = $petugas['id_admin'];
                $_SESSION['admin_username'] = $petugas['username'];
                $_SESSION['admin_nama'] = $petugas['nama_admin'];
                $_SESSION['admin_role'] = 'petugas';

                redirect('index.php');
            }
        }
        else {
            set_flash_message('danger', 'Username atau password salah!');
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Admin - Sistem Perpustakaan</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --dark-color: #212529;
            --light-color: #f8f9fa;
            --success-color: #4caf50;
            --danger-color: #f44336;
            --warning-color: #ff9800;
            --info-color: #2196f3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: #f0f2f5;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .background-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.2;
            border-radius: 50%;
        }

        .shape-1 {
            background: var(--primary-color);
            width: 300px;
            height: 300px;
            top: -150px;
            left: -150px;
        }

        .shape-2 {
            background: var(--accent-color);
            width: 200px;
            height: 200px;
            bottom: -100px;
            right: -100px;
        }

        .shape-3 {
            background: var(--secondary-color);
            width: 150px;
            height: 150px;
            bottom: 50%;
            left: 10%;
        }

        .shape-4 {
            background: var(--info-color);
            width: 80px;
            height: 80px;
            top: 30%;
            right: 10%;
        }

        .login-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 20px;
        }

        .login-container {
            display: flex;
            max-width: 900px;
            width: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .login-image {
            flex: 1;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .login-image::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(45deg);
        }

        .login-image-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .login-form {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            margin-bottom: 30px;
            text-align: center;
        }

        .login-header h2 {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        .login-header p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
        }

        .form-control-icon {
            position: relative;
        }

        .form-control-icon i {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .form-control {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 1px solid #ced4da;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
        }

        .btn-login {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn-login:hover {
            background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
            color: white;
        }

        .logo-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .logo-subtitle {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .features {
            margin-top: 30px;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .feature-icon {
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background-color: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: var(--danger-color);
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
            }

            .login-image {
                padding: 30px;
            }

            .login-form {
                padding: 30px;
            }

            .features {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="background-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
    </div>

    <div class="login-wrapper">
        <div class="login-container">
            <div class="login-image">
                <div class="login-image-content">
                    <div class="logo">
                        <i class="fas fa-book-reader"></i>
                    </div>
                    <div class="logo-title">PERPUSTAKAAN</div>
                    <div class="logo-subtitle">Sistem Manajemen Perpustakaan Digital</div>

                    <div class="features">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div>Manajemen Buku yang Efisien</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div>Peminjaman dan Pengembalian Mudah</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div>Laporan dan Statistik Lengkap</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div>Manajemen Anggota Terintegrasi</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="login-form">
                <div class="login-header">
                    <h2>Selamat Datang</h2>
                    <p>Silakan login untuk mengakses panel admin</p>
                </div>

                <?php display_flash_message(); ?>

                <form method="POST" action="">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="form-control-icon">
                            <i class="fas fa-user"></i>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Masukkan username" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="form-control-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Masukkan password" required>
                        </div>
                    </div>

                    <button type="submit" class="btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i> MASUK
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
