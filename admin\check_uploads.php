<?php
// File untuk memeriksa dan membuat direktori uploads jika belum ada

// Direktori uploads
$upload_dir = '../uploads/';
$books_dir = $upload_dir . 'books/';

// Periksa dan buat direktori uploads
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0777, true)) {
        echo "<p style='color: green;'>Direktori uploads berhasil dibuat!</p>";
    } else {
        echo "<p style='color: red;'>Gagal membuat direktori uploads!</p>";
    }
} else {
    echo "<p style='color: green;'>Direktori uploads sudah ada.</p>";
}

// Periksa dan buat direktori books
if (!is_dir($books_dir)) {
    if (mkdir($books_dir, 0777, true)) {
        echo "<p style='color: green;'>Direktori uploads/books berhasil dibuat!</p>";
    } else {
        echo "<p style='color: red;'>Gagal membuat direktori uploads/books!</p>";
    }
} else {
    echo "<p style='color: green;'>Direktori uploads/books sudah ada.</p>";
}

// Periksa izin direktori
if (is_writable($upload_dir)) {
    echo "<p style='color: green;'>Direktori uploads dapat ditulis.</p>";
} else {
    echo "<p style='color: red;'>Direktori uploads tidak dapat ditulis! Silakan ubah izin direktori.</p>";
}

if (is_writable($books_dir)) {
    echo "<p style='color: green;'>Direktori uploads/books dapat ditulis.</p>";
} else {
    echo "<p style='color: red;'>Direktori uploads/books tidak dapat ditulis! Silakan ubah izin direktori.</p>";
}

echo "<p>Kembali ke <a href='buku.php'>Manajemen Buku</a></p>";
?>
