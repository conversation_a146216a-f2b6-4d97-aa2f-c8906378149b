<?php
// Header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Memuat file koneksi database
require_once '../config/database.php';

// Query untuk mendapatkan daftar buku
$sql = "SELECT
            b.id_buku as id,
            b.judul,
            b.penulis,
            b.penerbit,
            b.tahun_terbit as tahunTerbit,
            COALESCE(k.nama_kategori, 'Tanpa Kategori') as kategori,
            b.jumlah_stok as stok,
            b.gambar_sampul as gambarSampul,
            b.desk<PERSON>si,
            4.5 as rating
        FROM buku b
        LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
        ORDER BY b.judul ASC";
$result = query($sql);

if ($result) {
    $books = fetch_all($result);

    // Debug: log data untuk melihat struktur
    error_log("Books API Response: " . json_encode($books));

    echo json_encode([
        'success' => true,
        'message' => 'Daftar buku berhasil diambil',
        'data' => $books
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mengambil daftar buku',
        'error' => mysqli_error($GLOBALS['connection'])
    ]);
}
?>
