<?php
// Header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Memuat file koneksi database
require_once '../config/database.php';

// Query untuk mendapatkan daftar buku
$sql = "SELECT b.*, k.nama_kategori 
        FROM buku b
        LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
        ORDER BY b.judul ASC";
$result = query($sql);

if ($result) {
    $books = fetch_all($result);
    
    echo json_encode([
        'success' => true,
        'message' => 'Daftar buku berhasil diambil',
        'data' => $books
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mengambil daftar buku'
    ]);
}
?>
