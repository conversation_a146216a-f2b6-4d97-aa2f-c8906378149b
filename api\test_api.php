<!DOCTYPE html>
<html>
<head>
    <title>Test API Books</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .api-test { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .json-output { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; }
        pre { margin: 0; white-space: pre-wrap; }
        .success { border-left: 4px solid #48bb78; }
        .error { border-left: 4px solid #f56565; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test API Books - Debug Kategori</h1>
    
    <div class="api-test">
        <h2>1. Test API Books List</h2>
        <?php
        $books_url = 'http://192.168.218.5/perpus_andro/api/books.php';
        $books_response = file_get_contents($books_url);
        $books_data = json_decode($books_response, true);
        
        echo "<p><strong>URL:</strong> $books_url</p>";
        
        if ($books_data && $books_data['success']) {
            echo "<p style='color: green;'>✅ API Response: SUCCESS</p>";
            echo "<p><strong>Message:</strong> {$books_data['message']}</p>";
            echo "<p><strong>Total Books:</strong> " . count($books_data['data']) . "</p>";
            
            if (!empty($books_data['data'])) {
                echo "<h3>Sample Book Data:</h3>";
                $sample_book = $books_data['data'][0];
                
                echo "<table>";
                foreach ($sample_book as $key => $value) {
                    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
                echo "</table>";
                
                echo "<h3>Kategori Check:</h3>";
                echo "<table>";
                echo "<tr><th>Judul</th><th>Kategori</th><th>Status</th></tr>";
                
                foreach ($books_data['data'] as $book) {
                    $kategori_status = !empty($book['kategori']) ? '✅ Ada' : '❌ Kosong';
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($book['judul']) . "</td>";
                    echo "<td>" . htmlspecialchars($book['kategori']) . "</td>";
                    echo "<td>$kategori_status</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p style='color: red;'>❌ API Response: FAILED</p>";
            if (isset($books_data['message'])) {
                echo "<p><strong>Error:</strong> {$books_data['message']}</p>";
            }
        }
        
        echo "<h3>Raw JSON Response:</h3>";
        echo "<div class='json-output'><pre>" . json_encode($books_data, JSON_PRETTY_PRINT) . "</pre></div>";
        ?>
    </div>
    
    <div class="api-test">
        <h2>2. Test Database Direct Query</h2>
        <?php
        require_once '../config/database.php';
        
        echo "<h3>Cek Tabel Kategori:</h3>";
        $kategori_sql = "SELECT * FROM kategori ORDER BY nama_kategori";
        $kategori_result = query($kategori_sql);
        
        if ($kategori_result && mysqli_num_rows($kategori_result) > 0) {
            echo "<p style='color: green;'>✅ Tabel kategori ditemukan</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Nama Kategori</th><th>Deskripsi</th></tr>";
            
            while ($row = mysqli_fetch_assoc($kategori_result)) {
                echo "<tr>";
                echo "<td>{$row['id_kategori']}</td>";
                echo "<td>{$row['nama_kategori']}</td>";
                echo "<td>" . ($row['deskripsi'] ?: '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Tabel kategori kosong atau tidak ada</p>";
        }
        
        echo "<h3>Cek Join Buku dengan Kategori:</h3>";
        $join_sql = "SELECT 
                        b.id_buku,
                        b.judul,
                        b.id_kategori,
                        k.nama_kategori,
                        COALESCE(k.nama_kategori, 'Tanpa Kategori') as kategori_final
                     FROM buku b
                     LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
                     ORDER BY b.judul
                     LIMIT 10";
        $join_result = query($join_sql);
        
        if ($join_result && mysqli_num_rows($join_result) > 0) {
            echo "<p style='color: green;'>✅ Join query berhasil</p>";
            echo "<table>";
            echo "<tr><th>ID Buku</th><th>Judul</th><th>ID Kategori</th><th>Nama Kategori</th><th>Kategori Final</th></tr>";
            
            while ($row = mysqli_fetch_assoc($join_result)) {
                echo "<tr>";
                echo "<td>{$row['id_buku']}</td>";
                echo "<td>{$row['judul']}</td>";
                echo "<td>" . ($row['id_kategori'] ?: 'NULL') . "</td>";
                echo "<td>" . ($row['nama_kategori'] ?: 'NULL') . "</td>";
                echo "<td>{$row['kategori_final']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Tidak ada data buku atau join gagal</p>";
        }
        ?>
    </div>
    
    <div class="api-test">
        <h2>3. Test Book Detail API</h2>
        <?php
        // Ambil ID buku pertama untuk test
        $first_book_sql = "SELECT id_buku FROM buku LIMIT 1";
        $first_book_result = query($first_book_sql);
        
        if ($first_book_result && mysqli_num_rows($first_book_result) > 0) {
            $first_book = mysqli_fetch_assoc($first_book_result);
            $book_id = $first_book['id_buku'];
            
            $detail_url = "http://192.168.218.5/perpus_andro/api/book_detail.php?id=$book_id";
            $detail_response = file_get_contents($detail_url);
            $detail_data = json_decode($detail_response, true);
            
            echo "<p><strong>URL:</strong> $detail_url</p>";
            
            if ($detail_data && $detail_data['success']) {
                echo "<p style='color: green;'>✅ Book Detail API: SUCCESS</p>";
                echo "<p><strong>Book ID:</strong> $book_id</p>";
                
                $book_detail = $detail_data['data'];
                echo "<table>";
                foreach ($book_detail as $key => $value) {
                    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
                echo "</table>";
            } else {
                echo "<p style='color: red;'>❌ Book Detail API: FAILED</p>";
                if (isset($detail_data['message'])) {
                    echo "<p><strong>Error:</strong> {$detail_data['message']}</p>";
                }
            }
            
            echo "<h3>Raw JSON Response:</h3>";
            echo "<div class='json-output'><pre>" . json_encode($detail_data, JSON_PRETTY_PRINT) . "</pre></div>";
        } else {
            echo "<p style='color: red;'>❌ Tidak ada buku untuk test detail API</p>";
        }
        ?>
    </div>
    
    <div class="api-test">
        <h2>4. Rekomendasi Perbaikan</h2>
        <ul>
            <li><strong>Pastikan tabel kategori ada data:</strong> Jalankan <a href="../admin/fix_kategori.php" target="_blank">fix_kategori.php</a></li>
            <li><strong>Cek koneksi database:</strong> Pastikan database perpus_db bisa diakses</li>
            <li><strong>Update aplikasi Android:</strong> Refresh data atau restart aplikasi</li>
            <li><strong>Cek log error:</strong> Lihat error log di C:\xampp\logs\php_error_log</li>
        </ul>
    </div>
    
    <p><a href="../admin/tambah_buku.php">← Kembali ke Admin</a></p>
</body>
</html>
