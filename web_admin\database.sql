CREATE DATABASE IF NOT EXISTS perpustakaan;
USE perpustakaan;

-- Tabel admin
CREATE TABLE admin (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> books
CREATE TABLE books (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    author VARCHA<PERSON>(100) NOT NULL,
    publisher VARCHAR(100),
    year VARCHAR(4),
    category VARCHAR(50),
    description TEXT,
    stock INT DEFAULT 0,
    image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON>bel users (untuk aplikasi Android)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'petugas') DEFAULT 'petugas',
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert admin default
INSERT INTO admin (username, password, name) VALUES 
('admin', MD5('admin123'), 'Administrator');

-- Insert user default untuk aplikasi Android
INSERT INTO users (username, password, role, name) VALUES 
('admin', MD5('admin123'), 'admin', 'Administrator'),
('petugas', MD5('petugas123'), 'petugas', 'Petugas Perpustakaan');

-- Insert sample books
INSERT INTO books (title, author, publisher, year, category, description, stock, image) VALUES 
('Pemrograman Android', 'John Doe', 'Informatika', '2023', 'Teknologi', 'Buku panduan lengkap pemrograman Android untuk pemula hingga mahir.', 5, 'android_book.jpg'),
('Sejarah Indonesia', 'Jane Smith', 'Gramedia', '2022', 'Sejarah', 'Sejarah lengkap Indonesia dari masa kerajaan hingga modern.', 3, 'sejarah_book.jpg'),
('Novel Laskar Pelangi', 'Andrea Hirata', 'Bentang Pustaka', '2021', 'Fiksi', 'Novel inspiratif tentang perjuangan anak-anak Belitung menggapai mimpi.', 7, 'laskar_pelangi.jpg'),
('Belajar Java', 'Robert Martin', 'Elex Media', '2023', 'Teknologi', 'Panduan praktis belajar pemrograman Java dari dasar hingga advanced.', 4, 'java_book.jpg'),
('Filosofi Hidup', 'Raden Adjeng', 'Mizan', '2022', 'Non-Fiksi', 'Renungan mendalam tentang makna hidup dan kebijaksanaan.', 2, 'filosofi_book.jpg');
