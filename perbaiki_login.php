<?php
// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'perpus_db';

echo "<h1><PERSON><PERSON><PERSON><PERSON>gin Perpustakaan</h1>";

// Koneksi ke MySQL
$conn = mysqli_connect($host, $username, $password);
if (!$conn) {
    die("<p style='color: red;'>Koneksi ke MySQL gagal: " . mysqli_connect_error() . "</p>");
}
echo "<p style='color: green;'>Koneksi ke MySQL berhasil!</p>";

// Cek apakah database perpus_db ada
$db_exists = mysqli_select_db($conn, $database);
if (!$db_exists) {
    echo "<p style='color: red;'>Database '$database' tidak ditemukan!</p>";
    echo "<p>Silakan import file SQL terlebih dahulu atau buat database baru.</p>";
    exit;
}
echo "<p style='color: green;'>Database '$database' ditemukan!</p>";

// Cek apakah tabel admin ada
$result = mysqli_query($conn, "SHOW TABLES LIKE 'admin'");
if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: red;'>Tabel 'admin' tidak ditemukan!</p>";
    echo "<p>Silakan import file SQL terlebih dahulu atau buat tabel admin.</p>";
    exit;
}
echo "<p style='color: green;'>Tabel 'admin' ditemukan!</p>";

// Hapus semua admin yang ada (untuk menghindari konflik)
$sql_delete = "DELETE FROM admin";
if (mysqli_query($conn, $sql_delete)) {
    echo "<p>Semua data admin lama dihapus!</p>";
} else {
    echo "<p style='color: red;'>Gagal menghapus data admin: " . mysqli_error($conn) . "</p>";
}

// Tambahkan admin baru dengan password yang pasti bekerja
$admin_username = 'admin';
$admin_password = 'admin123';
$admin_name = 'Administrator';
$admin_email = '<EMAIL>';

// Hash password dengan password_hash
$hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

// Tambah admin baru
$sql_insert = "INSERT INTO admin (username, password, nama_admin, email) 
              VALUES ('$admin_username', '$hashed_password', '$admin_name', '$admin_email')";

if (mysqli_query($conn, $sql_insert)) {
    echo "<p style='color: green;'>Admin baru berhasil ditambahkan!</p>";
} else {
    echo "<p style='color: red;'>Gagal menambahkan admin: " . mysqli_error($conn) . "</p>";
}

// Tambahkan petugas baru
$petugas_username = 'petugas';
$petugas_password = 'petugas123';
$petugas_name = 'Petugas Perpustakaan';
$petugas_email = '<EMAIL>';

// Hash password dengan password_hash
$hashed_password_petugas = password_hash($petugas_password, PASSWORD_DEFAULT);

// Tambah petugas baru
$sql_insert_petugas = "INSERT INTO admin (username, password, nama_admin, email) 
                      VALUES ('$petugas_username', '$hashed_password_petugas', '$petugas_name', '$petugas_email')";

if (mysqli_query($conn, $sql_insert_petugas)) {
    echo "<p style='color: green;'>Petugas baru berhasil ditambahkan!</p>";
} else {
    echo "<p style='color: red;'>Gagal menambahkan petugas: " . mysqli_error($conn) . "</p>";
}

// Verifikasi password admin
echo "<h2>Verifikasi Password Admin</h2>";

// Ambil data admin
$sql_admin = "SELECT * FROM admin WHERE username = 'admin'";
$result_admin = mysqli_query($conn, $sql_admin);

if ($result_admin && mysqli_num_rows($result_admin) > 0) {
    $admin = mysqli_fetch_assoc($result_admin);
    echo "<p>Admin ditemukan dengan ID: " . $admin['id_admin'] . "</p>";
    echo "<p>Password Hash: " . $admin['password'] . "</p>";
    
    // Verifikasi password
    if (password_verify($admin_password, $admin['password'])) {
        echo "<p style='color: green;'>Password admin berhasil diverifikasi!</p>";
    } else {
        echo "<p style='color: red;'>Password admin tidak cocok!</p>";
    }
} else {
    echo "<p style='color: red;'>Admin tidak ditemukan!</p>";
}

// Verifikasi password petugas
echo "<h2>Verifikasi Password Petugas</h2>";

// Ambil data petugas
$sql_petugas = "SELECT * FROM admin WHERE username = 'petugas'";
$result_petugas = mysqli_query($conn, $sql_petugas);

if ($result_petugas && mysqli_num_rows($result_petugas) > 0) {
    $petugas = mysqli_fetch_assoc($result_petugas);
    echo "<p>Petugas ditemukan dengan ID: " . $petugas['id_admin'] . "</p>";
    echo "<p>Password Hash: " . $petugas['password'] . "</p>";
    
    // Verifikasi password
    if (password_verify($petugas_password, $petugas['password'])) {
        echo "<p style='color: green;'>Password petugas berhasil diverifikasi!</p>";
    } else {
        echo "<p style='color: red;'>Password petugas tidak cocok!</p>";
    }
} else {
    echo "<p style='color: red;'>Petugas tidak ditemukan!</p>";
}

// Tutup koneksi
mysqli_close($conn);

echo "<h2>Informasi Login</h2>";
echo "<p>Admin:</p>";
echo "<ul>";
echo "<li>Username: <strong>admin</strong></li>";
echo "<li>Password: <strong>admin123</strong></li>";
echo "</ul>";

echo "<p>Petugas:</p>";
echo "<ul>";
echo "<li>Username: <strong>petugas</strong></li>";
echo "<li>Password: <strong>petugas123</strong></li>";
echo "</ul>";

echo "<h2>Langkah Selanjutnya</h2>";
echo "<p>Silakan <a href='admin/login.php'>login</a> dengan salah satu akun di atas.</p>";
?>
