<?php
// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'perpus_db';

echo "<h1>Login Bypass Perpustakaan</h1>";

// Koneksi ke MySQL
$conn = mysqli_connect($host, $username, $password);
if (!$conn) {
    die("<p style='color: red;'>Koneksi ke MySQL gagal: " . mysqli_connect_error() . "</p>");
}
echo "<p style='color: green;'>Koneksi ke MySQL berhasil!</p>";

// Cek apakah database perpus_db ada
$db_exists = mysqli_select_db($conn, $database);
if (!$db_exists) {
    echo "<p style='color: red;'>Database '$database' tidak ditemukan!</p>";
    echo "<p>Membuat database baru...</p>";
    
    // Buat database
    $sql_create_db = "CREATE DATABASE IF NOT EXISTS $database";
    if (mysqli_query($conn, $sql_create_db)) {
        echo "<p style='color: green;'>Database '$database' berhasil dibuat!</p>";
    } else {
        die("<p style='color: red;'>Gagal membuat database: " . mysqli_error($conn) . "</p>");
    }
}

// Pilih database
mysqli_select_db($conn, $database);
echo "<p style='color: green;'>Database '$database' dipilih!</p>";

// Cek apakah tabel admin ada
$result = mysqli_query($conn, "SHOW TABLES LIKE 'admin'");
if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: red;'>Tabel 'admin' tidak ditemukan!</p>";
    echo "<p>Membuat tabel admin...</p>";
    
    // Buat tabel admin
    $sql_create_table = "CREATE TABLE IF NOT EXISTS admin (
        id_admin INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        nama_admin VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        no_telp VARCHAR(15),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if (mysqli_query($conn, $sql_create_table)) {
        echo "<p style='color: green;'>Tabel 'admin' berhasil dibuat!</p>";
    } else {
        die("<p style='color: red;'>Gagal membuat tabel admin: " . mysqli_error($conn) . "</p>");
    }
}

// Tambahkan admin baru dengan password yang pasti bekerja
$admin_username = 'admin';
$admin_password = 'admin123';
$admin_name = 'Administrator';
$admin_email = '<EMAIL>';

// Hash password dengan password_hash
$hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

// Cek apakah admin sudah ada
$sql_check = "SELECT * FROM admin WHERE username = '$admin_username'";
$result_check = mysqli_query($conn, $sql_check);

if (mysqli_num_rows($result_check) > 0) {
    echo "<p>Admin dengan username '$admin_username' sudah ada.</p>";
    $admin = mysqli_fetch_assoc($result_check);
    $admin_id = $admin['id_admin'];
} else {
    // Tambah admin baru
    $sql_insert = "INSERT INTO admin (username, password, nama_admin, email) 
                  VALUES ('$admin_username', '$hashed_password', '$admin_name', '$admin_email')";
    
    if (mysqli_query($conn, $sql_insert)) {
        echo "<p style='color: green;'>Admin baru berhasil ditambahkan!</p>";
        $admin_id = mysqli_insert_id($conn);
    } else {
        echo "<p style='color: red;'>Gagal menambahkan admin: " . mysqli_error($conn) . "</p>";
        $admin_id = 1; // Default jika gagal
    }
}

// Tutup koneksi
mysqli_close($conn);

// Mulai session
session_start();

// Set session admin
$_SESSION['admin_id'] = $admin_id;
$_SESSION['admin_username'] = $admin_username;
$_SESSION['admin_nama'] = $admin_name;
$_SESSION['admin_role'] = 'admin';

echo "<p style='color: green;'>Session admin berhasil diatur!</p>";
echo "<p>ID Admin: " . $_SESSION['admin_id'] . "</p>";
echo "<p>Username: " . $_SESSION['admin_username'] . "</p>";
echo "<p>Nama: " . $_SESSION['admin_nama'] . "</p>";
echo "<p>Role: " . $_SESSION['admin_role'] . "</p>";

echo "<h2>Login Berhasil!</h2>";
echo "<p>Anda akan diarahkan ke dashboard admin dalam 3 detik...</p>";
echo "<p>Jika tidak diarahkan secara otomatis, silakan klik <a href='admin/index.php'>di sini</a>.</p>";
?>

<script>
    // Redirect ke dashboard admin setelah 3 detik
    setTimeout(function() {
        window.location.href = 'admin/index.php';
    }, 3000);
</script>
