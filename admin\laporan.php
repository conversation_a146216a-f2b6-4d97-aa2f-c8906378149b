<?php
require_once 'includes/header.php';

// Filter tanggal
$tanggal_mulai = isset($_GET['tanggal_mulai']) ? clean($_GET['tanggal_mulai']) : date('Y-m-01'); // Awal bulan ini
$tanggal_selesai = isset($_GET['tanggal_selesai']) ? clean($_GET['tanggal_selesai']) : date('Y-m-d'); // Hari ini

// Filter jenis laporan
$jenis_laporan = isset($_GET['jenis']) ? clean($_GET['jenis']) : 'peminjaman';

// Query untuk laporan peminjaman
if ($jenis_laporan == 'peminjaman') {
    $sql = "SELECT p.*, a.nama_lengkap, b.judul 
            FROM peminjaman p
            JOIN anggota a ON p.id_anggota = a.id_anggota
            JOIN buku b ON p.id_buku = b.id_buku
            WHERE p.tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
            ORDER BY p.tanggal_pinjam DESC";
    
    $result = query($sql);
    $data_laporan = fetch_all($result);
    
    // Hitung statistik
    $total_peminjaman = count($data_laporan);
    
    $sql_status = "SELECT status_peminjaman, COUNT(*) as jumlah 
                  FROM peminjaman 
                  WHERE tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
                  GROUP BY status_peminjaman";
    $result_status = query($sql_status);
    $data_status = fetch_all($result_status);
    
    $status_count = [
        'menunggu' => 0,
        'dipinjam' => 0,
        'dikembalikan' => 0,
        'terlambat' => 0,
        'ditolak' => 0
    ];
    
    foreach ($data_status as $status) {
        $status_count[$status['status_peminjaman']] = $status['jumlah'];
    }
    
    // Hitung total denda
    $sql_denda = "SELECT SUM(denda) as total_denda 
                 FROM peminjaman 
                 WHERE tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'";
    $result_denda = query($sql_denda);
    $data_denda = fetch_assoc($result_denda);
    $total_denda = $data_denda['total_denda'] ?? 0;
}
// Query untuk laporan buku terpopuler
elseif ($jenis_laporan == 'buku_populer') {
    $sql = "SELECT b.id_buku, b.judul, b.penulis, COUNT(p.id_peminjaman) as jumlah_peminjaman
            FROM buku b
            LEFT JOIN peminjaman p ON b.id_buku = p.id_buku
            WHERE p.tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
            GROUP BY b.id_buku
            ORDER BY jumlah_peminjaman DESC
            LIMIT 10";
    
    $result = query($sql);
    $data_laporan = fetch_all($result);
}
// Query untuk laporan anggota teraktif
elseif ($jenis_laporan == 'anggota_aktif') {
    $sql = "SELECT a.id_anggota, a.nama_lengkap, a.username, COUNT(p.id_peminjaman) as jumlah_peminjaman
            FROM anggota a
            LEFT JOIN peminjaman p ON a.id_anggota = p.id_anggota
            WHERE p.tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
            GROUP BY a.id_anggota
            ORDER BY jumlah_peminjaman DESC
            LIMIT 10";
    
    $result = query($sql);
    $data_laporan = fetch_all($result);
}
?>

<!-- Laporan Content -->
<div class="container-fluid">
    <!-- Filter Laporan -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Laporan</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="jenis" class="form-label">Jenis Laporan</label>
                            <select class="form-select" id="jenis" name="jenis">
                                <option value="peminjaman" <?php echo $jenis_laporan == 'peminjaman' ? 'selected' : ''; ?>>Laporan Peminjaman</option>
                                <option value="buku_populer" <?php echo $jenis_laporan == 'buku_populer' ? 'selected' : ''; ?>>Buku Terpopuler</option>
                                <option value="anggota_aktif" <?php echo $jenis_laporan == 'anggota_aktif' ? 'selected' : ''; ?>>Anggota Teraktif</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="tanggal_mulai" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" value="<?php echo $tanggal_mulai; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="tanggal_selesai" class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control" id="tanggal_selesai" name="tanggal_selesai" value="<?php echo $tanggal_selesai; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">Tampilkan</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php if ($jenis_laporan == 'peminjaman'): ?>
    <!-- Statistik Peminjaman -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Peminjaman</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_peminjaman; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Denda</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Dikembalikan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $status_count['dikembalikan']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Terlambat</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $status_count['terlambat']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Laporan Peminjaman -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Laporan Peminjaman</h6>
            <a href="cetak_laporan.php?jenis=peminjaman&tanggal_mulai=<?php echo $tanggal_mulai; ?>&tanggal_selesai=<?php echo $tanggal_selesai; ?>" class="btn btn-sm btn-primary" target="_blank">
                <i class="fas fa-print"></i> Cetak
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Anggota</th>
                            <th>Buku</th>
                            <th>Tanggal Pinjam</th>
                            <th>Tanggal Kembali</th>
                            <th>Status</th>
                            <th>Denda</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if (count($data_laporan) > 0) {
                            foreach ($data_laporan as $p) {
                                // Set warna status
                                $status_class = '';
                                switch ($p['status_peminjaman']) {
                                    case 'menunggu':
                                        $status_class = 'warning';
                                        break;
                                    case 'dipinjam':
                                        $status_class = 'primary';
                                        break;
                                    case 'dikembalikan':
                                        $status_class = 'success';
                                        break;
                                    case 'terlambat':
                                        $status_class = 'danger';
                                        break;
                                    case 'ditolak':
                                        $status_class = 'secondary';
                                        break;
                                }
                                
                                echo "<tr>
                                        <td>{$p['id_peminjaman']}</td>
                                        <td>{$p['nama_lengkap']}</td>
                                        <td>{$p['judul']}</td>
                                        <td>" . format_tanggal($p['tanggal_pinjam']) . "</td>
                                        <td>" . format_tanggal($p['tanggal_kembali']) . "</td>
                                        <td><span class='badge bg-{$status_class}'>{$p['status_peminjaman']}</span></td>
                                        <td>" . ($p['denda'] > 0 ? 'Rp ' . number_format($p['denda'], 0, ',', '.') : '-') . "</td>
                                    </tr>";
                            }
                        } else {
                            echo "<tr><td colspan='7' class='text-center'>Tidak ada data peminjaman</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php elseif ($jenis_laporan == 'buku_populer'): ?>
    <!-- Laporan Buku Terpopuler -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Laporan Buku Terpopuler</h6>
            <a href="cetak_laporan.php?jenis=buku_populer&tanggal_mulai=<?php echo $tanggal_mulai; ?>&tanggal_selesai=<?php echo $tanggal_selesai; ?>" class="btn btn-sm btn-primary" target="_blank">
                <i class="fas fa-print"></i> Cetak
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Judul Buku</th>
                            <th>Penulis</th>
                            <th>Jumlah Peminjaman</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if (count($data_laporan) > 0) {
                            $no = 1;
                            foreach ($data_laporan as $b) {
                                echo "<tr>
                                        <td>{$no}</td>
                                        <td>{$b['judul']}</td>
                                        <td>{$b['penulis']}</td>
                                        <td>{$b['jumlah_peminjaman']}</td>
                                    </tr>";
                                $no++;
                            }
                        } else {
                            echo "<tr><td colspan='4' class='text-center'>Tidak ada data</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php elseif ($jenis_laporan == 'anggota_aktif'): ?>
    <!-- Laporan Anggota Teraktif -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Laporan Anggota Teraktif</h6>
            <a href="cetak_laporan.php?jenis=anggota_aktif&tanggal_mulai=<?php echo $tanggal_mulai; ?>&tanggal_selesai=<?php echo $tanggal_selesai; ?>" class="btn btn-sm btn-primary" target="_blank">
                <i class="fas fa-print"></i> Cetak
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Nama Anggota</th>
                            <th>Username</th>
                            <th>Jumlah Peminjaman</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if (count($data_laporan) > 0) {
                            $no = 1;
                            foreach ($data_laporan as $a) {
                                echo "<tr>
                                        <td>{$no}</td>
                                        <td>{$a['nama_lengkap']}</td>
                                        <td>{$a['username']}</td>
                                        <td>{$a['jumlah_peminjaman']}</td>
                                    </tr>";
                                $no++;
                            }
                        } else {
                            echo "<tr><td colspan='4' class='text-center'>Tidak ada data</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php require_once 'includes/footer.php'; ?>
