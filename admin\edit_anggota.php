<?php
require_once 'includes/header.php';

// Cek ID anggota
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'ID anggota tidak valid!');
    redirect('anggota.php');
}

$id_anggota = clean($_GET['id']);

// Ambil data anggota
$sql = "SELECT * FROM anggota WHERE id_anggota = $id_anggota";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Anggota tidak ditemukan!');
    redirect('anggota.php');
}

$anggota = fetch_assoc($result);

// Proses edit anggota
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = clean($_POST['username']);
    $nama_lengkap = clean($_POST['nama_lengkap']);
    $email = clean($_POST['email']);
    $no_telp = clean($_POST['no_telp']);
    $alamat = clean($_POST['alamat']);
    $status = clean($_POST['status']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validasi input
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'Username harus diisi!';
    }
    
    if (empty($nama_lengkap)) {
        $errors[] = 'Nama lengkap harus diisi!';
    }
    
    // Cek apakah username sudah digunakan oleh anggota lain
    $sql_check = "SELECT * FROM anggota WHERE username = '$username' AND id_anggota != $id_anggota";
    $result_check = query($sql_check);
    
    if (num_rows($result_check) > 0) {
        $errors[] = 'Username sudah digunakan oleh anggota lain!';
    }
    
    // Jika tidak ada error, update data anggota
    if (empty($errors)) {
        // Jika password diisi, update password juga
        if (!empty($password)) {
            // Validasi konfirmasi password
            if ($password !== $confirm_password) {
                $errors[] = 'Konfirmasi password tidak cocok!';
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Update data anggota dengan password baru
                $sql_update = "UPDATE anggota SET 
                              username = '$username', 
                              password = '$hashed_password', 
                              nama_lengkap = '$nama_lengkap', 
                              email = '$email', 
                              no_telp = '$no_telp', 
                              alamat = '$alamat', 
                              status = '$status'
                              WHERE id_anggota = $id_anggota";
            }
        } else {
            // Update data anggota tanpa mengubah password
            $sql_update = "UPDATE anggota SET 
                          username = '$username', 
                          nama_lengkap = '$nama_lengkap', 
                          email = '$email', 
                          no_telp = '$no_telp', 
                          alamat = '$alamat', 
                          status = '$status'
                          WHERE id_anggota = $id_anggota";
        }
        
        if (empty($errors)) {
            $result_update = query($sql_update);
            
            if ($result_update) {
                set_flash_message('success', 'Data anggota berhasil diperbarui!');
                redirect('anggota.php');
            } else {
                $errors[] = 'Gagal memperbarui data anggota!';
            }
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}
?>

<!-- Edit Anggota Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Edit Anggota</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo $anggota['username']; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password <small class="text-muted">(Kosongkan jika tidak ingin mengubah)</small></label>
                            <input type="password" class="form-control" id="password" name="password">
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Konfirmasi Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>
                        <div class="mb-3">
                            <label for="nama_lengkap" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" value="<?php echo $anggota['nama_lengkap']; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $anggota['email']; ?>">
                        </div>
                        <div class="mb-3">
                            <label for="no_telp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="no_telp" name="no_telp" value="<?php echo $anggota['no_telp']; ?>">
                        </div>
                        <div class="mb-3">
                            <label for="alamat" class="form-label">Alamat</label>
                            <textarea class="form-control" id="alamat" name="alamat" rows="3"><?php echo $anggota['alamat']; ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="aktif" <?php echo $anggota['status'] == 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                                <option value="nonaktif" <?php echo $anggota['status'] == 'nonaktif' ? 'selected' : ''; ?>>Nonaktif</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <a href="anggota.php" class="btn btn-secondary me-2">Batal</a>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
