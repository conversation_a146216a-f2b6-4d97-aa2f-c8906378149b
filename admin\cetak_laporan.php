<?php
require_once '../config/functions.php';

// Cek jenis laporan
if (!isset($_GET['jenis']) || empty($_GET['jenis'])) {
    die('<PERSON>is laporan tidak valid!');
}

$jenis_laporan = clean($_GET['jenis']);
$tanggal_mulai = isset($_GET['tanggal_mulai']) ? clean($_GET['tanggal_mulai']) : date('Y-m-01');
$tanggal_selesai = isset($_GET['tanggal_selesai']) ? clean($_GET['tanggal_selesai']) : date('Y-m-d');

// Query untuk laporan peminjaman
if ($jenis_laporan == 'peminjaman') {
    $sql = "SELECT p.*, a.nama_lengkap, b.judul 
            FROM peminjaman p
            JOIN anggota a ON p.id_anggota = a.id_anggota
            JOIN buku b ON p.id_buku = b.id_buku
            WHERE p.tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
            ORDER BY p.tanggal_pinjam DESC";
    
    $result = query($sql);
    $data_laporan = fetch_all($result);
    
    // Hitung statistik
    $total_peminjaman = count($data_laporan);
    
    $sql_status = "SELECT status_peminjaman, COUNT(*) as jumlah 
                  FROM peminjaman 
                  WHERE tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
                  GROUP BY status_peminjaman";
    $result_status = query($sql_status);
    $data_status = fetch_all($result_status);
    
    $status_count = [
        'menunggu' => 0,
        'dipinjam' => 0,
        'dikembalikan' => 0,
        'terlambat' => 0,
        'ditolak' => 0
    ];
    
    foreach ($data_status as $status) {
        $status_count[$status['status_peminjaman']] = $status['jumlah'];
    }
    
    // Hitung total denda
    $sql_denda = "SELECT SUM(denda) as total_denda 
                 FROM peminjaman 
                 WHERE tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'";
    $result_denda = query($sql_denda);
    $data_denda = fetch_assoc($result_denda);
    $total_denda = $data_denda['total_denda'] ?? 0;
    
    $judul_laporan = "Laporan Peminjaman Buku";
}
// Query untuk laporan buku terpopuler
elseif ($jenis_laporan == 'buku_populer') {
    $sql = "SELECT b.id_buku, b.judul, b.penulis, COUNT(p.id_peminjaman) as jumlah_peminjaman
            FROM buku b
            LEFT JOIN peminjaman p ON b.id_buku = p.id_buku
            WHERE p.tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
            GROUP BY b.id_buku
            ORDER BY jumlah_peminjaman DESC
            LIMIT 10";
    
    $result = query($sql);
    $data_laporan = fetch_all($result);
    
    $judul_laporan = "Laporan Buku Terpopuler";
}
// Query untuk laporan anggota teraktif
elseif ($jenis_laporan == 'anggota_aktif') {
    $sql = "SELECT a.id_anggota, a.nama_lengkap, a.username, COUNT(p.id_peminjaman) as jumlah_peminjaman
            FROM anggota a
            LEFT JOIN peminjaman p ON a.id_anggota = p.id_anggota
            WHERE p.tanggal_pinjam BETWEEN '$tanggal_mulai' AND '$tanggal_selesai'
            GROUP BY a.id_anggota
            ORDER BY jumlah_peminjaman DESC
            LIMIT 10";
    
    $result = query($sql);
    $data_laporan = fetch_all($result);
    
    $judul_laporan = "Laporan Anggota Teraktif";
} else {
    die('Jenis laporan tidak valid!');
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $judul_laporan; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            padding: 0;
        }
        .header p {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
        }
        .summary {
            margin-bottom: 20px;
        }
        .summary table {
            width: auto;
            margin-right: 20px;
            display: inline-block;
            vertical-align: top;
        }
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>PERPUSTAKAAN</h2>
        <p><?php echo $judul_laporan; ?></p>
        <p>Periode: <?php echo format_tanggal($tanggal_mulai); ?> - <?php echo format_tanggal($tanggal_selesai); ?></p>
    </div>
    
    <?php if ($jenis_laporan == 'peminjaman'): ?>
    <div class="summary">
        <table>
            <tr>
                <th colspan="2">Ringkasan</th>
            </tr>
            <tr>
                <td>Total Peminjaman</td>
                <td><?php echo $total_peminjaman; ?></td>
            </tr>
            <tr>
                <td>Menunggu</td>
                <td><?php echo $status_count['menunggu']; ?></td>
            </tr>
            <tr>
                <td>Dipinjam</td>
                <td><?php echo $status_count['dipinjam']; ?></td>
            </tr>
            <tr>
                <td>Dikembalikan</td>
                <td><?php echo $status_count['dikembalikan']; ?></td>
            </tr>
            <tr>
                <td>Terlambat</td>
                <td><?php echo $status_count['terlambat']; ?></td>
            </tr>
            <tr>
                <td>Ditolak</td>
                <td><?php echo $status_count['ditolak']; ?></td>
            </tr>
            <tr>
                <td>Total Denda</td>
                <td>Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></td>
            </tr>
        </table>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>ID</th>
                <th>Anggota</th>
                <th>Buku</th>
                <th>Tanggal Pinjam</th>
                <th>Tanggal Kembali</th>
                <th>Status</th>
                <th>Denda</th>
            </tr>
        </thead>
        <tbody>
            <?php
            if (count($data_laporan) > 0) {
                $no = 1;
                foreach ($data_laporan as $p) {
                    echo "<tr>
                            <td>{$no}</td>
                            <td>{$p['id_peminjaman']}</td>
                            <td>{$p['nama_lengkap']}</td>
                            <td>{$p['judul']}</td>
                            <td>" . format_tanggal($p['tanggal_pinjam']) . "</td>
                            <td>" . format_tanggal($p['tanggal_kembali']) . "</td>
                            <td>{$p['status_peminjaman']}</td>
                            <td>" . ($p['denda'] > 0 ? 'Rp ' . number_format($p['denda'], 0, ',', '.') : '-') . "</td>
                        </tr>";
                    $no++;
                }
            } else {
                echo "<tr><td colspan='8' style='text-align: center;'>Tidak ada data peminjaman</td></tr>";
            }
            ?>
        </tbody>
    </table>
    <?php elseif ($jenis_laporan == 'buku_populer'): ?>
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Judul Buku</th>
                <th>Penulis</th>
                <th>Jumlah Peminjaman</th>
            </tr>
        </thead>
        <tbody>
            <?php
            if (count($data_laporan) > 0) {
                $no = 1;
                foreach ($data_laporan as $b) {
                    echo "<tr>
                            <td>{$no}</td>
                            <td>{$b['judul']}</td>
                            <td>{$b['penulis']}</td>
                            <td>{$b['jumlah_peminjaman']}</td>
                        </tr>";
                    $no++;
                }
            } else {
                echo "<tr><td colspan='4' style='text-align: center;'>Tidak ada data</td></tr>";
            }
            ?>
        </tbody>
    </table>
    <?php elseif ($jenis_laporan == 'anggota_aktif'): ?>
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Nama Anggota</th>
                <th>Username</th>
                <th>Jumlah Peminjaman</th>
            </tr>
        </thead>
        <tbody>
            <?php
            if (count($data_laporan) > 0) {
                $no = 1;
                foreach ($data_laporan as $a) {
                    echo "<tr>
                            <td>{$no}</td>
                            <td>{$a['nama_lengkap']}</td>
                            <td>{$a['username']}</td>
                            <td>{$a['jumlah_peminjaman']}</td>
                        </tr>";
                    $no++;
                }
            } else {
                echo "<tr><td colspan='4' style='text-align: center;'>Tidak ada data</td></tr>";
            }
            ?>
        </tbody>
    </table>
    <?php endif; ?>
    
    <div class="footer">
        <p>Dicetak pada: <?php echo date('d/m/Y H:i:s'); ?></p>
    </div>
    
    <div class="no-print">
        <button onclick="window.print()">Cetak</button>
        <button onclick="window.close()">Tutup</button>
    </div>
    
    <script>
        // Auto print
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
<?php
// Tutup koneksi database
close_connection();
?>
