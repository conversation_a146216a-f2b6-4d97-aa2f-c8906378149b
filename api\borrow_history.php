<?php
// Header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Memuat file koneksi database
require_once '../config/database.php';

// Ambil ID anggota dari parameter
$id_anggota = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

// Validasi input
if ($id_anggota <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID anggota tidak valid'
    ]);
    exit;
}

// Query untuk mendapatkan riwayat peminjaman
$sql = "SELECT p.*, b.judul, b.penulis, b.gambar_sampul 
        FROM peminjaman p
        JOIN buku b ON p.id_buku = b.id_buku
        WHERE p.id_anggota = $id_anggota
        ORDER BY p.tanggal_pinjam DESC";
$result = query($sql);

if ($result) {
    $borrows = fetch_all($result);
    
    echo json_encode([
        'success' => true,
        'message' => 'Riwayat peminjaman berhasil diambil',
        'data' => $borrows
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mengambil riwayat peminjaman'
    ]);
}
?>
