<?php
// Header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');

// Memuat file koneksi database
require_once '../config/database.php';

// Cek metode request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Metode tidak diizinkan'
    ]);
    exit;
}

// Ambil data dari request
$id_anggota = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
$id_buku = isset($_POST['book_id']) ? (int)$_POST['book_id'] : 0;
$tanggal_pinjam = isset($_POST['tanggal_pinjam']) ? clean($_POST['tanggal_pinjam']) : date('Y-m-d');
$tanggal_kembali = isset($_POST['tanggal_kembali']) ? clean($_POST['tanggal_kembali']) : date('Y-m-d', strtotime('+7 days'));

// Validasi input
if ($id_anggota <= 0 || $id_buku <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'ID anggota dan ID buku harus valid'
    ]);
    exit;
}

// Cek ketersediaan buku
$sql = "SELECT jumlah_stok FROM buku WHERE id_buku = $id_buku";
$result = query($sql);

if (num_rows($result) === 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Buku tidak ditemukan'
    ]);
    exit;
}

$book = fetch_assoc($result);
if ($book['jumlah_stok'] <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Stok buku habis'
    ]);
    exit;
}

// Cek apakah anggota sudah meminjam buku yang sama dan belum dikembalikan
$sql = "SELECT * FROM peminjaman 
        WHERE id_anggota = $id_anggota 
        AND id_buku = $id_buku 
        AND status_peminjaman IN ('menunggu', 'dipinjam')";
$result = query($sql);

if (num_rows($result) > 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Anda sudah meminjam atau mengajukan peminjaman untuk buku ini'
    ]);
    exit;
}

// Tambahkan peminjaman
$sql = "INSERT INTO peminjaman (id_anggota, id_buku, tanggal_pinjam, tanggal_kembali, status_peminjaman) 
        VALUES ($id_anggota, $id_buku, '$tanggal_pinjam', '$tanggal_kembali', 'menunggu')";
$result = query($sql);

if ($result) {
    $id_peminjaman = insert_id();
    
    // Ambil data peminjaman
    $sql = "SELECT p.*, b.judul, b.gambar_sampul 
            FROM peminjaman p
            JOIN buku b ON p.id_buku = b.id_buku
            WHERE p.id_peminjaman = $id_peminjaman";
    $result = query($sql);
    $borrow = fetch_assoc($result);
    
    echo json_encode([
        'success' => true,
        'message' => 'Permintaan peminjaman berhasil dikirim',
        'data' => $borrow
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mengirim permintaan peminjaman'
    ]);
}
?>
