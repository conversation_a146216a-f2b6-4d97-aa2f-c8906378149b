-- Database: perpus_db
-- Struktur database untuk aplikasi perpustakaan

-- Hapus database jika sudah ada
DROP DATABASE IF EXISTS perpus_db;

-- Buat database baru
CREATE DATABASE perpus_db;

-- Gunakan database
USE perpus_db;

-- Ha<PERSON> fungsi jika sudah ada
DROP FUNCTION IF EXISTS count_books;
DROP FUNCTION IF EXISTS count_members;
DROP FUNCTION IF EXISTS count_loans;

-- <PERSON><PERSON> admin
CREATE TABLE admin (
    id_admin INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nama_admin VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    no_telp VARCHAR(15),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> kate<PERSON><PERSON>
CREATE TABLE kategori (
    id_kategori INT AUTO_INCREMENT PRIMARY KEY,
    nama_kategori VARCHAR(50) NOT NULL,
    deskripsi TEXT
);

-- Tabel anggota
CREATE TABLE anggota (
    id_anggota INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    no_telp VARCHAR(15),
    alamat TEXT,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel buku
CREATE TABLE buku (
    id_buku INT AUTO_INCREMENT PRIMARY KEY,
    judul VARCHAR(100) NOT NULL,
    penulis VARCHAR(100) NOT NULL,
    penerbit VARCHAR(100),
    tahun_terbit YEAR,
    isbn VARCHAR(20),
    id_kategori INT,
    jumlah_stok INT DEFAULT 0,
    deskripsi TEXT,
    gambar_sampul VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kategori) REFERENCES kategori(id_kategori) ON DELETE SET NULL
);

-- Tabel peminjaman
CREATE TABLE peminjaman (
    id_peminjaman INT AUTO_INCREMENT PRIMARY KEY,
    id_anggota INT,
    id_buku INT,
    tanggal_pinjam DATE NOT NULL,
    tanggal_kembali DATE NOT NULL,
    tanggal_pengembalian DATE,
    status_peminjaman ENUM('menunggu', 'dipinjam', 'dikembalikan', 'terlambat', 'ditolak') DEFAULT 'menunggu',
    denda DECIMAL(10,2) DEFAULT 0.00,
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_anggota) REFERENCES anggota(id_anggota) ON DELETE CASCADE,
    FOREIGN KEY (id_buku) REFERENCES buku(id_buku) ON DELETE CASCADE
);

-- Insert data admin default
INSERT INTO admin (username, password, nama_admin, email) 
VALUES ('admin', '$2y$10$Uj7OLwOH9Vkf9H1XzLIz3.aQnwCBVWYJSXAA7hgOgPUECCRCqn.Oe', 'Administrator', '<EMAIL>');
-- Password: admin123 (sudah di-hash dengan bcrypt)

-- Insert data petugas default
INSERT INTO admin (username, password, nama_admin, email) 
VALUES ('petugas', '$2y$10$3Rl.LFhX14zMbpZfPT0KAeGGdnuqUjW0xYzB1QOvFRRdxAUQXwmHe', 'Petugas Perpustakaan', '<EMAIL>');
-- Password: petugas123 (sudah di-hash dengan bcrypt)

-- Insert data kategori
INSERT INTO kategori (nama_kategori, deskripsi) VALUES 
('Fiksi', 'Buku-buku fiksi termasuk novel, cerpen, dll'),
('Non-Fiksi', 'Buku-buku non-fiksi seperti biografi, sejarah, dll'),
('Pendidikan', 'Buku-buku pendidikan dan akademik'),
('Teknologi', 'Buku-buku tentang teknologi dan komputer'),
('Sastra', 'Buku-buku sastra klasik dan modern');

-- Insert data anggota
INSERT INTO anggota (username, password, nama_lengkap, email, no_telp, alamat, status) VALUES
('johndoe', '$2y$10$Uj7OLwOH9Vkf9H1XzLIz3.aQnwCBVWYJSXAA7hgOgPUECCRCqn.Oe', 'John Doe', '<EMAIL>', '081234567890', 'Jl. Contoh No. 123', 'aktif'),
('janedoe', '$2y$10$Uj7OLwOH9Vkf9H1XzLIz3.aQnwCBVWYJSXAA7hgOgPUECCRCqn.Oe', 'Jane Doe', '<EMAIL>', '089876543210', 'Jl. Sample No. 456', 'aktif');
-- Password: admin123 (sama dengan admin untuk kemudahan testing)

-- Insert data buku
INSERT INTO buku (judul, penulis, penerbit, tahun_terbit, isbn, id_kategori, jumlah_stok, deskripsi) VALUES
('Harry Potter and the Philosopher''s Stone', 'J.K. Rowling', 'Bloomsbury', 1997, '9780747532743', 1, 5, 'Buku pertama dari seri Harry Potter'),
('To Kill a Mockingbird', 'Harper Lee', 'J.B. Lippincott & Co.', 1960, '9780061120084', 1, 3, 'Novel klasik tentang rasisme dan ketidakadilan'),
('A Brief History of Time', 'Stephen Hawking', 'Bantam Books', 1988, '9780553380163', 2, 2, 'Buku tentang kosmologi untuk pembaca umum'),
('Algoritma dan Pemrograman', 'Rinaldi Munir', 'Informatika', 2011, '9789792910902', 3, 8, 'Buku dasar algoritma dan pemrograman'),
('Laskar Pelangi', 'Andrea Hirata', 'Bentang Pustaka', 2005, '9789793062792', 5, 4, 'Novel inspiratif tentang perjuangan pendidikan');

-- Insert data peminjaman
INSERT INTO peminjaman (id_anggota, id_buku, tanggal_pinjam, tanggal_kembali, status_peminjaman) VALUES
(1, 1, '2023-01-10', '2023-01-17', 'dikembalikan'),
(1, 3, '2023-02-05', '2023-02-12', 'dipinjam'),
(2, 2, '2023-01-20', '2023-01-27', 'dikembalikan'),
(2, 4, '2023-02-15', '2023-02-22', 'dipinjam');

-- Update tanggal pengembalian untuk peminjaman yang sudah dikembalikan
UPDATE peminjaman SET tanggal_pengembalian = '2023-01-16' WHERE id_anggota = 1 AND id_buku = 1;
UPDATE peminjaman SET tanggal_pengembalian = '2023-01-28', denda = 5000 WHERE id_anggota = 2 AND id_buku = 2;

-- Fungsi untuk menghitung jumlah buku
DELIMITER //
CREATE FUNCTION count_books() RETURNS INT
BEGIN
    DECLARE total INT;
    SELECT COUNT(*) INTO total FROM buku;
    RETURN total;
END //
DELIMITER ;

-- Fungsi untuk menghitung jumlah anggota
DELIMITER //
CREATE FUNCTION count_members() RETURNS INT
BEGIN
    DECLARE total INT;
    SELECT COUNT(*) INTO total FROM anggota;
    RETURN total;
END //
DELIMITER ;

-- Fungsi untuk menghitung jumlah peminjaman
DELIMITER //
CREATE FUNCTION count_loans(status VARCHAR(20)) RETURNS INT
BEGIN
    DECLARE total INT;
    
    IF status IS NULL THEN
        SELECT COUNT(*) INTO total FROM peminjaman;
    ELSE
        SELECT COUNT(*) INTO total FROM peminjaman WHERE status_peminjaman = status;
    END IF;
    
    RETURN total;
END //
DELIMITER ;
