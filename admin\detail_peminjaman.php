<?php
require_once 'includes/header.php';

// Cek ID peminjaman
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'ID peminjaman tidak valid!');
    redirect('peminjaman.php');
}

$id_peminjaman = clean($_GET['id']);

// Ambil data peminjaman
$sql = "SELECT p.*, a.nama_lengkap, a.email, a.no_telp, b.judul, b.penulis, b.gambar_sampul 
        FROM peminjaman p
        JOIN anggota a ON p.id_anggota = a.id_anggota
        JOIN buku b ON p.id_buku = b.id_buku
        WHERE p.id_peminjaman = $id_peminjaman";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Peminjaman tidak ditemukan!');
    redirect('peminjaman.php');
}

$peminjaman = fetch_assoc($result);

// Set warna status
$status_class = '';
switch ($peminjaman['status_peminjaman']) {
    case 'menunggu':
        $status_class = 'warning';
        break;
    case 'dipinjam':
        $status_class = 'primary';
        break;
    case 'dikembalikan':
        $status_class = 'success';
        break;
    case 'terlambat':
        $status_class = 'danger';
        break;
    case 'ditolak':
        $status_class = 'secondary';
        break;
}
?>

<!-- Detail Peminjaman Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Detail Peminjaman #<?php echo $peminjaman['id_peminjaman']; ?></h6>
            <div>
                <?php if ($peminjaman['status_peminjaman'] == 'menunggu'): ?>
                    <a href="proses_peminjaman.php?id=<?php echo $peminjaman['id_peminjaman']; ?>&action=approve" class="btn btn-sm btn-success">
                        <i class="fas fa-check"></i> Setujui
                    </a>
                    <a href="proses_peminjaman.php?id=<?php echo $peminjaman['id_peminjaman']; ?>&action=reject" class="btn btn-sm btn-danger">
                        <i class="fas fa-times"></i> Tolak
                    </a>
                <?php elseif ($peminjaman['status_peminjaman'] == 'dipinjam'): ?>
                    <a href="proses_peminjaman.php?id=<?php echo $peminjaman['id_peminjaman']; ?>&action=return" class="btn btn-sm btn-success">
                        <i class="fas fa-undo"></i> Kembalikan
                    </a>
                <?php endif; ?>
                <a href="peminjaman.php" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold">Informasi Peminjaman</h6>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th width="40%">Status</th>
                                    <td><span class="badge bg-<?php echo $status_class; ?>"><?php echo $peminjaman['status_peminjaman']; ?></span></td>
                                </tr>
                                <tr>
                                    <th>Tanggal Pinjam</th>
                                    <td><?php echo format_tanggal($peminjaman['tanggal_pinjam']); ?></td>
                                </tr>
                                <tr>
                                    <th>Tanggal Kembali</th>
                                    <td><?php echo format_tanggal($peminjaman['tanggal_kembali']); ?></td>
                                </tr>
                                <?php if (!empty($peminjaman['tanggal_pengembalian'])): ?>
                                <tr>
                                    <th>Tanggal Pengembalian</th>
                                    <td><?php echo format_tanggal($peminjaman['tanggal_pengembalian']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($peminjaman['denda'] > 0): ?>
                                <tr>
                                    <th>Denda</th>
                                    <td>Rp <?php echo number_format($peminjaman['denda'], 0, ',', '.'); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($peminjaman['catatan'])): ?>
                                <tr>
                                    <th>Catatan</th>
                                    <td><?php echo nl2br($peminjaman['catatan']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <th>Tanggal Dibuat</th>
                                    <td><?php echo date('d/m/Y H:i', strtotime($peminjaman['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <th>Terakhir Diperbarui</th>
                                    <td><?php echo date('d/m/Y H:i', strtotime($peminjaman['updated_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold">Informasi Anggota</h6>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th width="40%">Nama</th>
                                    <td><?php echo $peminjaman['nama_lengkap']; ?></td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td><?php echo $peminjaman['email']; ?></td>
                                </tr>
                                <tr>
                                    <th>No. Telepon</th>
                                    <td><?php echo $peminjaman['no_telp']; ?></td>
                                </tr>
                                <tr>
                                    <th>Aksi</th>
                                    <td>
                                        <a href="detail_anggota.php?id=<?php echo $peminjaman['id_anggota']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Lihat Detail
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold">Informasi Buku</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center mb-3">
                                    <?php if (!empty($peminjaman['gambar_sampul']) && file_exists('../' . $peminjaman['gambar_sampul'])): ?>
                                        <img src="../<?php echo $peminjaman['gambar_sampul']; ?>" alt="Sampul Buku" class="img-fluid rounded" style="max-height: 200px;">
                                    <?php else: ?>
                                        <img src="https://via.placeholder.com/200x300?text=No+Cover" alt="No Cover" class="img-fluid rounded">
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-9">
                                    <h5><?php echo $peminjaman['judul']; ?></h5>
                                    <p>Penulis: <?php echo $peminjaman['penulis']; ?></p>
                                    <a href="detail_buku.php?id=<?php echo $peminjaman['id_buku']; ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Lihat Detail Buku
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
