<?php
require_once 'includes/header.php';

// Cek ID admin
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'ID admin tidak valid!');
    redirect('daftar_admin.php');
}

$id_admin = clean($_GET['id']);

// Ambil data admin
$sql = "SELECT * FROM admin WHERE id_admin = $id_admin";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Admin tidak ditemukan!');
    redirect('daftar_admin.php');
}

$admin_data = fetch_assoc($result);

// Proses edit admin
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = clean($_POST['username']);
    $nama_admin = clean($_POST['nama_admin']);
    $email = clean($_POST['email']);
    $no_telp = clean($_POST['no_telp']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validasi input
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'Username harus diisi!';
    }
    
    if (empty($nama_admin)) {
        $errors[] = 'Nama admin harus diisi!';
    }
    
    // Cek apakah username sudah digunakan oleh admin lain
    $sql_check = "SELECT * FROM admin WHERE username = '$username' AND id_admin != $id_admin";
    $result_check = query($sql_check);
    
    if (num_rows($result_check) > 0) {
        $errors[] = 'Username sudah digunakan oleh admin lain!';
    }
    
    // Jika tidak ada error, update data admin
    if (empty($errors)) {
        // Jika password diisi, update password juga
        if (!empty($password)) {
            // Validasi konfirmasi password
            if ($password !== $confirm_password) {
                $errors[] = 'Konfirmasi password tidak cocok!';
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Update data admin dengan password baru
                $sql_update = "UPDATE admin SET 
                              username = '$username', 
                              password = '$hashed_password', 
                              nama_admin = '$nama_admin', 
                              email = '$email', 
                              no_telp = '$no_telp'
                              WHERE id_admin = $id_admin";
            }
        } else {
            // Update data admin tanpa mengubah password
            $sql_update = "UPDATE admin SET 
                          username = '$username', 
                          nama_admin = '$nama_admin', 
                          email = '$email', 
                          no_telp = '$no_telp'
                          WHERE id_admin = $id_admin";
        }
        
        if (empty($errors)) {
            $result_update = query($sql_update);
            
            if ($result_update) {
                // Jika admin yang diedit adalah admin yang sedang login, update session
                if ($id_admin == $_SESSION['admin_id']) {
                    $_SESSION['admin_username'] = $username;
                    $_SESSION['admin_nama'] = $nama_admin;
                }
                
                set_flash_message('success', 'Data admin berhasil diperbarui!');
                redirect('daftar_admin.php');
            } else {
                $errors[] = 'Gagal memperbarui data admin!';
            }
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}
?>

<!-- Edit Admin Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Edit Admin</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo $admin_data['username']; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password <small class="text-muted">(Kosongkan jika tidak ingin mengubah)</small></label>
                            <input type="password" class="form-control" id="password" name="password">
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Konfirmasi Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nama_admin" class="form-label">Nama Admin <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_admin" name="nama_admin" value="<?php echo $admin_data['nama_admin']; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $admin_data['email']; ?>">
                        </div>
                        <div class="mb-3">
                            <label for="no_telp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="no_telp" name="no_telp" value="<?php echo $admin_data['no_telp']; ?>">
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <a href="daftar_admin.php" class="btn btn-secondary me-2">Batal</a>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
