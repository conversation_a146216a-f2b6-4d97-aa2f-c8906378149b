<?php
require_once 'includes/header.php';

// Proses tambah peminjaman
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_anggota = clean($_POST['id_anggota']);
    $id_buku = clean($_POST['id_buku']);
    $tanggal_pinjam = clean($_POST['tanggal_pinjam']);
    $tanggal_kembali = clean($_POST['tanggal_kembali']);
    $catatan = clean($_POST['catatan'] ?? '');
    
    // Validasi input
    $errors = [];
    
    if (empty($id_anggota)) {
        $errors[] = 'Anggota harus dipilih!';
    }
    
    if (empty($id_buku)) {
        $errors[] = 'Buku harus dipilih!';
    }
    
    if (empty($tanggal_pinjam)) {
        $errors[] = 'Tanggal pinjam harus diisi!';
    }
    
    if (empty($tanggal_kembali)) {
        $errors[] = 'Tanggal kembali harus diisi!';
    }
    
    // Validasi tanggal
    $date_pinjam = new DateTime($tanggal_pinjam);
    $date_kembali = new DateTime($tanggal_kembali);
    $date_now = new DateTime();
    
    if ($date_kembali <= $date_pinjam) {
        $errors[] = 'Tanggal kembali harus setelah tanggal pinjam!';
    }
    
    // Cek stok buku
    if (!empty($id_buku)) {
        $sql_buku = "SELECT jumlah_stok FROM buku WHERE id_buku = $id_buku";
        $result_buku = query($sql_buku);
        $buku = fetch_assoc($result_buku);
        
        if ($buku['jumlah_stok'] <= 0) {
            $errors[] = 'Stok buku tidak tersedia!';
        }
    }
    
    // Cek apakah anggota sudah meminjam buku yang sama
    if (!empty($id_anggota) && !empty($id_buku)) {
        $sql_cek = "SELECT * FROM peminjaman 
                   WHERE id_anggota = $id_anggota 
                   AND id_buku = $id_buku 
                   AND status_peminjaman IN ('menunggu', 'dipinjam')";
        $result_cek = query($sql_cek);
        
        if (num_rows($result_cek) > 0) {
            $errors[] = 'Anggota sudah meminjam atau mengajukan peminjaman buku ini!';
        }
    }
    
    // Jika tidak ada error, simpan data peminjaman
    if (empty($errors)) {
        // Simpan data peminjaman
        $sql = "INSERT INTO peminjaman (id_anggota, id_buku, tanggal_pinjam, tanggal_kembali, status_peminjaman, catatan) 
                VALUES ($id_anggota, $id_buku, '$tanggal_pinjam', '$tanggal_kembali', 'dipinjam', '$catatan')";
        
        $result = query($sql);
        
        if ($result) {
            // Kurangi stok buku
            $sql_update_stok = "UPDATE buku SET jumlah_stok = jumlah_stok - 1 WHERE id_buku = $id_buku";
            query($sql_update_stok);
            
            set_flash_message('success', 'Peminjaman berhasil ditambahkan!');
            redirect('peminjaman.php');
        } else {
            $errors[] = 'Gagal menambahkan peminjaman!';
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}

// Ambil data anggota
$sql_anggota = "SELECT * FROM anggota WHERE status = 'aktif' ORDER BY nama_lengkap ASC";
$result_anggota = query($sql_anggota);
$anggota = fetch_all($result_anggota);

// Ambil data buku
$sql_buku = "SELECT * FROM buku WHERE jumlah_stok > 0 ORDER BY judul ASC";
$result_buku = query($sql_buku);
$buku = fetch_all($result_buku);
?>

<!-- Tambah Peminjaman Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Tambah Peminjaman Baru</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_anggota" class="form-label">Anggota <span class="text-danger">*</span></label>
                            <select class="form-select" id="id_anggota" name="id_anggota" required>
                                <option value="">-- Pilih Anggota --</option>
                                <?php foreach ($anggota as $a): ?>
                                <option value="<?php echo $a['id_anggota']; ?>"><?php echo $a['nama_lengkap']; ?> (<?php echo $a['username']; ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="id_buku" class="form-label">Buku <span class="text-danger">*</span></label>
                            <select class="form-select" id="id_buku" name="id_buku" required>
                                <option value="">-- Pilih Buku --</option>
                                <?php foreach ($buku as $b): ?>
                                <option value="<?php echo $b['id_buku']; ?>"><?php echo $b['judul']; ?> (Stok: <?php echo $b['jumlah_stok']; ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tanggal_pinjam" class="form-label">Tanggal Pinjam <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal_pinjam" name="tanggal_pinjam" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="tanggal_kembali" class="form-label">Tanggal Kembali <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal_kembali" name="tanggal_kembali" value="<?php echo date('Y-m-d', strtotime('+7 days')); ?>" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="catatan" class="form-label">Catatan</label>
                    <textarea class="form-control" id="catatan" name="catatan" rows="3"></textarea>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <a href="peminjaman.php" class="btn btn-secondary me-2">Batal</a>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
