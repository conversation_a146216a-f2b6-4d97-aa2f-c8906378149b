<?php
require_once 'includes/header.php';

// Proses tambah buku
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $judul = clean($_POST['judul']);
    $penulis = clean($_POST['penulis']);
    $penerbit = clean($_POST['penerbit']);
    $tahun_terbit = clean($_POST['tahun_terbit']);
    $isbn = clean($_POST['isbn']);
    $id_kategori = clean($_POST['id_kategori']);
    $jumlah_stok = clean($_POST['jumlah_stok']);
    $deskripsi = clean($_POST['deskripsi']);
    
    // Validasi input
    $errors = [];
    
    if (empty($judul)) {
        $errors[] = 'Judul buku harus diisi!';
    }
    
    if (empty($penulis)) {
        $errors[] = 'Penulis buku harus diisi!';
    }
    
    if (empty($jumlah_stok) || !is_numeric($jumlah_stok) || $jumlah_stok < 0) {
        $errors[] = 'Jumlah stok harus diisi dengan angka yang valid!';
    }
    
    // Jika tidak ada error, simpan data buku
    if (empty($errors)) {
        // Upload gambar sampul jika ada
        $gambar_sampul = '';
        
        if (isset($_FILES['gambar_sampul']) && $_FILES['gambar_sampul']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/jpg'];
            $max_size = 2 * 1024 * 1024; // 2MB
            
            $file_name = $_FILES['gambar_sampul']['name'];
            $file_size = $_FILES['gambar_sampul']['size'];
            $file_tmp = $_FILES['gambar_sampul']['tmp_name'];
            $file_type = $_FILES['gambar_sampul']['type'];
            
            // Cek tipe file
            if (!in_array($file_type, $allowed_types)) {
                $errors[] = 'Tipe file tidak didukung. Hanya JPG, JPEG, dan PNG yang diperbolehkan!';
            }
            
            // Cek ukuran file
            if ($file_size > $max_size) {
                $errors[] = 'Ukuran file terlalu besar. Maksimal 2MB!';
            }
            
            if (empty($errors)) {
                // Generate nama file unik
                $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);
                $new_file_name = 'book_' . time() . '.' . $file_ext;
                
                // Buat direktori jika belum ada
                $upload_dir = '../uploads/books/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                
                // Upload file
                if (move_uploaded_file($file_tmp, $upload_dir . $new_file_name)) {
                    $gambar_sampul = 'uploads/books/' . $new_file_name;
                } else {
                    $errors[] = 'Gagal mengupload file!';
                }
            }
        }
        
        if (empty($errors)) {
            // Simpan data buku
            $sql = "INSERT INTO buku (judul, penulis, penerbit, tahun_terbit, isbn, id_kategori, jumlah_stok, deskripsi, gambar_sampul) 
                    VALUES ('$judul', '$penulis', '$penerbit', '$tahun_terbit', '$isbn', '$id_kategori', '$jumlah_stok', '$deskripsi', '$gambar_sampul')";
            
            $result = query($sql);
            
            if ($result) {
                set_flash_message('success', 'Buku berhasil ditambahkan!');
                redirect('buku.php');
            } else {
                $errors[] = 'Gagal menambahkan buku!';
            }
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}

// Ambil data kategori
$sql_kategori = "SELECT * FROM kategori ORDER BY nama_kategori ASC";
$result_kategori = query($sql_kategori);
$kategori = fetch_all($result_kategori);
?>

<!-- Tambah Buku Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Tambah Buku Baru</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="judul" class="form-label">Judul Buku <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="judul" name="judul" required>
                        </div>
                        <div class="mb-3">
                            <label for="penulis" class="form-label">Penulis <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="penulis" name="penulis" required>
                        </div>
                        <div class="mb-3">
                            <label for="penerbit" class="form-label">Penerbit</label>
                            <input type="text" class="form-control" id="penerbit" name="penerbit">
                        </div>
                        <div class="mb-3">
                            <label for="tahun_terbit" class="form-label">Tahun Terbit</label>
                            <input type="number" class="form-control" id="tahun_terbit" name="tahun_terbit" min="1900" max="<?php echo date('Y'); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="isbn" class="form-label">ISBN</label>
                            <input type="text" class="form-control" id="isbn" name="isbn">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_kategori" class="form-label">Kategori</label>
                            <select class="form-select" id="id_kategori" name="id_kategori">
                                <option value="">-- Pilih Kategori --</option>
                                <?php foreach ($kategori as $k): ?>
                                <option value="<?php echo $k['id_kategori']; ?>"><?php echo $k['nama_kategori']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="jumlah_stok" class="form-label">Jumlah Stok <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="jumlah_stok" name="jumlah_stok" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="gambar_sampul" class="form-label">Gambar Sampul</label>
                            <input type="file" class="form-control" id="gambar_sampul" name="gambar_sampul">
                            <small class="text-muted">Format: JPG, JPEG, PNG. Maks: 2MB</small>
                        </div>
                        <div class="mb-3">
                            <label for="deskripsi" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="deskripsi" name="deskripsi" rows="5"></textarea>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <a href="buku.php" class="btn btn-secondary me-2">Batal</a>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

