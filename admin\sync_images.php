<?php
require_once 'includes/header.php';
require_once 'upload_helper.php';

$message = '';
$message_type = '';

// Proses sinkronisasi jika diminta
if (isset($_POST['sync_images'])) {
    $result = resyncExistingImages();
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'danger';
}

// Ambil daftar file di folder utama
$main_dir = '../uploads/books/';
$files = [];
if (is_dir($main_dir)) {
    $files = glob($main_dir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
}

// Cek status sinkronisasi untuk setiap file
$sync_status = [];
foreach ($files as $file) {
    $filename = basename($file);
    $sync_status[$filename] = checkImageSync($filename);
}
?>

<!-- Sync Images Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Sinkronisasi Gambar Buku</h6>
        </div>
        <div class="card-body">
            <?php if ($message): ?>
                <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
                    <?= $message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Status Folder</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Folder</th>
                                    <th>Status</th>
                                    <th>Jumlah File</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $folders = [
                                    '../uploads/books/' => 'Folder Utama',
                                    '../api/uploads/books/' => 'Folder API',
                                    '../web_admin/uploads/' => 'Folder Web Admin'
                                ];
                                
                                foreach ($folders as $path => $name):
                                    $exists = is_dir($path);
                                    $count = $exists ? count(glob($path . '*.{jpg,jpeg,png,gif}', GLOB_BRACE)) : 0;
                                ?>
                                <tr>
                                    <td><?= $name ?></td>
                                    <td>
                                        <?php if ($exists): ?>
                                            <span class="badge bg-success">Ada</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Tidak Ada</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $count ?> file</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5>Aksi Sinkronisasi</h5>
                    <form method="POST">
                        <div class="d-grid">
                            <button type="submit" name="sync_images" class="btn btn-primary">
                                <i class="fas fa-sync"></i> Sinkronisasi Semua Gambar
                            </button>
                        </div>
                    </form>
                    <small class="text-muted mt-2 d-block">
                        Akan menyalin semua gambar dari folder utama ke folder API dan Web Admin
                    </small>
                </div>
            </div>
            
            <h5>Detail File Gambar</h5>
            <?php if (empty($files)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Tidak ada file gambar di folder utama.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nama File</th>
                                <th>Folder Utama</th>
                                <th>Folder API</th>
                                <th>Folder Web Admin</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sync_status as $filename => $status): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($filename) ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?= date('Y-m-d H:i:s', filemtime($main_dir . $filename)) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($status['../uploads/books/']): ?>
                                            <span class="badge bg-success">✓</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($status['../api/uploads/books/']): ?>
                                            <span class="badge bg-success">✓</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($status['../web_admin/uploads/']): ?>
                                            <span class="badge bg-success">✓</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $all_synced = $status['../uploads/books/'] && 
                                                     $status['../api/uploads/books/'] && 
                                                     $status['../web_admin/uploads/'];
                                        ?>
                                        <?php if ($all_synced): ?>
                                            <span class="badge bg-success">Tersinkronisasi</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Perlu Sinkronisasi</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <div class="mt-4">
                <h6>Informasi:</h6>
                <ul class="text-muted">
                    <li><strong>Folder Utama:</strong> Tempat penyimpanan utama gambar buku</li>
                    <li><strong>Folder API:</strong> Digunakan oleh aplikasi Android untuk mengakses gambar</li>
                    <li><strong>Folder Web Admin:</strong> Digunakan oleh panel admin web untuk menampilkan gambar</li>
                    <li><strong>Sinkronisasi:</strong> Memastikan semua folder memiliki file gambar yang sama</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
