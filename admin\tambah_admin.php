<?php
require_once 'includes/header.php';

// Proses tambah admin
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = clean($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $nama_admin = clean($_POST['nama_admin']);
    $email = clean($_POST['email']);
    $no_telp = clean($_POST['no_telp']);
    
    // Validasi input
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'Username harus diisi!';
    }
    
    if (empty($password)) {
        $errors[] = 'Password harus diisi!';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Konfirmasi password tidak cocok!';
    }
    
    if (empty($nama_admin)) {
        $errors[] = 'Nama admin harus diisi!';
    }
    
    // Cek apakah username sudah digunakan
    $sql_check = "SELECT * FROM admin WHERE username = '$username'";
    $result_check = query($sql_check);
    
    if (num_rows($result_check) > 0) {
        $errors[] = 'Username sudah digunakan!';
    }
    
    // Jika tidak ada error, simpan data admin
    if (empty($errors)) {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Simpan data admin
        $sql = "INSERT INTO admin (username, password, nama_admin, email, no_telp) 
                VALUES ('$username', '$hashed_password', '$nama_admin', '$email', '$no_telp')";
        
        $result = query($sql);
        
        if ($result) {
            set_flash_message('success', 'Admin berhasil ditambahkan!');
            redirect('daftar_admin.php');
        } else {
            $errors[] = 'Gagal menambahkan admin!';
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}
?>

<!-- Tambah Admin Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Tambah Admin Baru</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Konfirmasi Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nama_admin" class="form-label">Nama Admin <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_admin" name="nama_admin" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="mb-3">
                            <label for="no_telp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="no_telp" name="no_telp">
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <a href="daftar_admin.php" class="btn btn-secondary me-2">Batal</a>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
