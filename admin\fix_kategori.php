<?php
require_once '../config/database.php';

echo "<h2>Fix Kategori Database</h2>";

// Cek apakah tabel kategori ada
$check_table = "SHOW TABLES LIKE 'kategori'";
$result = query($check_table);

if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: red;'>Tabel kategori tidak ditemukan. Membuat tabel...</p>";
    
    // Buat tabel kategori
    $create_table = "CREATE TABLE kategori (
        id_kategori int(11) NOT NULL AUTO_INCREMENT,
        nama_kategori varchar(100) NOT NULL,
        deskripsi text,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id_kategori)
    )";
    
    if (query($create_table)) {
        echo "<p style='color: green;'>Tabel kategori berhasil dibuat!</p>";
    } else {
        echo "<p style='color: red;'>Gagal membuat tabel kategori!</p>";
        exit;
    }
}

// Cek isi tabel kategori
$check_data = "SELECT * FROM kategori";
$result = query($check_data);

echo "<h3>Data Kategori Saat Ini:</h3>";
if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: orange;'>Tabel kategori kosong. Menambahkan data default...</p>";
    
    // Insert data kategori default
    $default_categories = [
        ['Teknologi', 'Buku-buku tentang teknologi dan komputer'],
        ['Fiksi', 'Novel dan cerita fiksi'],
        ['Non-Fiksi', 'Buku-buku faktual dan edukatif'],
        ['Sejarah', 'Buku-buku sejarah dan biografi'],
        ['Sains', 'Buku-buku sains dan penelitian'],
        ['Pendidikan', 'Buku-buku pendidikan dan pembelajaran'],
        ['Agama', 'Buku-buku keagamaan'],
        ['Ekonomi', 'Buku-buku ekonomi dan bisnis'],
        ['Kesehatan', 'Buku-buku kesehatan dan medis'],
        ['Seni', 'Buku-buku seni dan budaya']
    ];
    
    foreach ($default_categories as $category) {
        $insert_sql = "INSERT INTO kategori (nama_kategori, deskripsi) VALUES ('{$category[0]}', '{$category[1]}')";
        if (query($insert_sql)) {
            echo "<p style='color: green;'>✓ Kategori '{$category[0]}' berhasil ditambahkan</p>";
        } else {
            echo "<p style='color: red;'>✗ Gagal menambahkan kategori '{$category[0]}'</p>";
        }
    }
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nama Kategori</th><th>Deskripsi</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>{$row['id_kategori']}</td>";
        echo "<td>{$row['nama_kategori']}</td>";
        echo "<td>{$row['deskripsi']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Cek foreign key constraint
echo "<h3>Cek Foreign Key Constraint:</h3>";
$check_fk = "SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE CONSTRAINT_SCHEMA = 'perpus_db' 
AND TABLE_NAME = 'buku' 
AND REFERENCED_TABLE_NAME IS NOT NULL";

$fk_result = query($check_fk);
if (mysqli_num_rows($fk_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Constraint</th><th>Table</th><th>Column</th><th>Referenced Table</th><th>Referenced Column</th></tr>";
    
    while ($row = mysqli_fetch_assoc($fk_result)) {
        echo "<tr>";
        echo "<td>{$row['CONSTRAINT_NAME']}</td>";
        echo "<td>{$row['TABLE_NAME']}</td>";
        echo "<td>{$row['COLUMN_NAME']}</td>";
        echo "<td>{$row['REFERENCED_TABLE_NAME']}</td>";
        echo "<td>{$row['REFERENCED_COLUMN_NAME']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>Tidak ada foreign key constraint ditemukan.</p>";
}

// Test insert buku
echo "<h3>Test Insert Buku:</h3>";
$test_kategori = "SELECT id_kategori FROM kategori LIMIT 1";
$test_result = query($test_kategori);

if (mysqli_num_rows($test_result) > 0) {
    $kategori_row = mysqli_fetch_assoc($test_result);
    $test_id = $kategori_row['id_kategori'];
    
    echo "<p>Menggunakan kategori ID: $test_id untuk test</p>";
    
    // Test query (tanpa execute)
    $test_sql = "INSERT INTO buku (judul, penulis, penerbit, tahun_terbit, isbn, id_kategori, jumlah_stok, deskripsi, gambar_sampul) 
                VALUES ('Test Book', 'Test Author', 'Test Publisher', 2023, '123456789', $test_id, 1, 'Test Description', '')";
    
    echo "<p><strong>Test SQL:</strong></p>";
    echo "<pre>$test_sql</pre>";
    
    // Coba execute test
    if (query($test_sql)) {
        echo "<p style='color: green;'>✓ Test insert berhasil! Database sudah siap.</p>";
        
        // Hapus data test
        $delete_test = "DELETE FROM buku WHERE judul = 'Test Book'";
        query($delete_test);
        echo "<p style='color: blue;'>Data test dihapus.</p>";
    } else {
        echo "<p style='color: red;'>✗ Test insert gagal!</p>";
    }
} else {
    echo "<p style='color: red;'>Tidak ada kategori untuk test!</p>";
}

echo "<hr>";
echo "<p><a href='tambah_buku.php'>← Kembali ke Tambah Buku</a></p>";
echo "<p><a href='kategori.php'>→ Kelola Kategori</a></p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
</style>
