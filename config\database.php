<?php
/**
 * Konfigurasi koneksi database
 */

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'perpus_db';

// Membuat koneksi ke MySQL
$conn_mysql = mysqli_connect($host, $username, $password);

// Cek koneksi ke MySQL
if (!$conn_mysql) {
    die("Koneksi ke MySQL gagal: " . mysqli_connect_error() .
        "<br>Pastikan MySQL server sudah berjalan dan username/password benar.");
}

// Cek apakah database sudah ada
$db_exists = mysqli_select_db($conn_mysql, $database);
if (!$db_exists) {
    // Redirect ke halaman check_database.php
    header("Location: ../check_database.php");
    exit();
}

// Tutup koneksi ke MySQL
mysqli_close($conn_mysql);

// Membuat koneksi ke database
$conn = mysqli_connect($host, $username, $password, $database);

// Cek koneksi ke database
if (!$conn) {
    die("Koneksi database gagal: " . mysqli_connect_error());
}

// Set charset ke utf8
mysqli_set_charset($conn, "utf8");

// Fungsi untuk membersihkan input
function clean($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    $data = mysqli_real_escape_string($conn, $data);
    return $data;
}

// Fungsi untuk menjalankan query dan mengembalikan hasil
function query($sql) {
    global $conn;
    $result = mysqli_query($conn, $sql);
    return $result;
}

// Fungsi untuk mendapatkan satu baris data
function fetch_assoc($result) {
    return mysqli_fetch_assoc($result);
}

// Fungsi untuk mendapatkan semua baris data
function fetch_all($result) {
    $data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    return $data;
}

// Fungsi untuk mendapatkan jumlah baris
function num_rows($result) {
    return mysqli_num_rows($result);
}

// Fungsi untuk mendapatkan ID terakhir yang diinsert
function insert_id() {
    global $conn;
    return mysqli_insert_id($conn);
}

// Fungsi untuk mendapatkan jumlah baris yang terpengaruh
function affected_rows() {
    global $conn;
    return mysqli_affected_rows($conn);
}

// Fungsi untuk menutup koneksi
function close_connection() {
    global $conn;
    mysqli_close($conn);
}
?>
