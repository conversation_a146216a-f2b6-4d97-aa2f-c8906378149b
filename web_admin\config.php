<?php
// Konfigurasi database
$host = 'localhost';
$dbname = 'perpustakaan';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Koneksi gagal: " . $e->getMessage());
}

// Fungsi upload gambar ke multiple folder
function uploadImageMultiFolder($file) {
    // Tentukan path absolut berdasarkan lokasi file ini
    $base_path = dirname(__DIR__); // Naik satu level dari web_admin

    // Daftar folder tujuan - sesuaikan dengan struktur yang ada
    $upload_dirs = [
        __DIR__ . "/uploads/",                    // web_admin/uploads/ (buat baru)
        $base_path . "/api/uploads/books/",       // api/uploads/books/ (yang sudah ada)
        $base_path . "/uploads/books/"            // uploads/books/ (yang sudah ada)
    ];

    // Debug: tampilkan path yang akan digunakan
    error_log("Base path: " . $base_path);
    foreach ($upload_dirs as $i => $dir) {
        error_log("Upload dir $i: " . $dir);
    }

    // Buat semua folder jika belum ada
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            $created = mkdir($dir, 0777, true);
            error_log("Created directory $dir: " . ($created ? "SUCCESS" : "FAILED"));
        } else {
            error_log("Directory already exists: $dir");
        }
    }

    $imageFileType = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
    $newFileName = uniqid() . '_' . time() . '.' . $imageFileType;

    // Validasi file
    $check = getimagesize($file["tmp_name"]);
    if($check === false) {
        return ['success' => false, 'message' => 'File bukan gambar yang valid'];
    }

    // Cek ukuran file (maksimal 5MB)
    if ($file["size"] > 5000000) {
        return ['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 5MB)'];
    }

    // Cek format file
    $allowed_types = ["jpg", "jpeg", "png", "gif"];
    if(!in_array($imageFileType, $allowed_types)) {
        return ['success' => false, 'message' => 'Format file tidak diizinkan. Gunakan JPG, PNG, atau GIF'];
    }

    // Upload ke folder pertama (web admin)
    $main_target = $upload_dirs[0] . $newFileName;
    if (move_uploaded_file($file["tmp_name"], $main_target)) {
        $uploaded_paths = [$main_target];

        // Copy ke folder lainnya
        foreach (array_slice($upload_dirs, 1) as $dir) {
            $target_path = $dir . $newFileName;
            if (copy($main_target, $target_path)) {
                $uploaded_paths[] = $target_path;
            } else {
                error_log("Gagal copy file ke: " . $target_path);
            }
        }

        return [
            'success' => true,
            'filename' => $newFileName,
            'paths' => $uploaded_paths,
            'message' => 'File berhasil diupload ke ' . count($uploaded_paths) . ' lokasi'
        ];
    } else {
        return ['success' => false, 'message' => 'Gagal mengupload file'];
    }
}

// Fungsi hapus gambar dari semua folder
function deleteImageMultiFolder($filename) {
    // Gunakan path yang sama dengan upload
    $base_path = dirname(__DIR__);

    $upload_dirs = [
        __DIR__ . "/uploads/",                    // web_admin/uploads/
        $base_path . "/api/uploads/books/",       // api/uploads/books/
        $base_path . "/uploads/books/"            // uploads/books/
    ];

    $deleted_count = 0;
    foreach ($upload_dirs as $dir) {
        $file_path = $dir . $filename;
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                $deleted_count++;
                error_log("Deleted file: " . $file_path);
            } else {
                error_log("Failed to delete file: " . $file_path);
            }
        } else {
            error_log("File not found: " . $file_path);
        }
    }

    return $deleted_count;
}

// Fungsi cek login admin
function checkAdminLogin() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: login.php');
        exit();
    }
}

session_start();
?>
