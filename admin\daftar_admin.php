<?php
require_once 'includes/header.php';

// Proses hapus admin
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id_admin = clean($_GET['id']);
    
    // Cek apakah admin yang akan dihapus adalah admin yang sedang login
    if ($id_admin == $_SESSION['admin_id']) {
        set_flash_message('danger', 'Anda tidak dapat menghapus akun admin yang sedang digunakan!');
    } else {
        // Hapus admin
        $sql_delete = "DELETE FROM admin WHERE id_admin = $id_admin";
        $result_delete = query($sql_delete);
        
        if ($result_delete) {
            set_flash_message('success', 'Admin berhasil dihapus!');
        } else {
            set_flash_message('danger', 'Gagal menghapus admin!');
        }
    }
    
    redirect('daftar_admin.php');
}
?>

<!-- Daftar Admin Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Admin</h6>
            <a href="tambah_admin.php" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Tambah Admin
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Nama Admin</th>
                            <th>Email</th>
                            <th>No. Telepon</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Ambil data admin
                        $sql = "SELECT * FROM admin ORDER BY id_admin DESC";
                        $result = query($sql);
                        $admin_list = fetch_all($result);
                        
                        if (count($admin_list) > 0) {
                            foreach ($admin_list as $admin) {
                                echo "<tr>
                                        <td>{$admin['id_admin']}</td>
                                        <td>{$admin['username']}</td>
                                        <td>{$admin['nama_admin']}</td>
                                        <td>{$admin['email']}</td>
                                        <td>{$admin['no_telp']}</td>
                                        <td>";
                                
                                // Jika bukan admin yang sedang login, tampilkan tombol hapus
                                if ($admin['id_admin'] != $_SESSION['admin_id']) {
                                    echo "<a href='edit_admin.php?id={$admin['id_admin']}' class='btn btn-sm btn-warning'>
                                            <i class='fas fa-edit'></i>
                                          </a>
                                          <a href='javascript:void(0);' onclick='confirmDelete({$admin['id_admin']})' class='btn btn-sm btn-danger'>
                                            <i class='fas fa-trash'></i>
                                          </a>";
                                } else {
                                    echo "<a href='edit_admin.php?id={$admin['id_admin']}' class='btn btn-sm btn-warning'>
                                            <i class='fas fa-edit'></i>
                                          </a>
                                          <button class='btn btn-sm btn-secondary' disabled>
                                            <i class='fas fa-user'></i> Akun Aktif
                                          </button>";
                                }
                                
                                echo "</td>
                                    </tr>";
                            }
                        } else {
                            echo "<tr><td colspan='6' class='text-center'>Tidak ada data admin</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Script untuk konfirmasi hapus -->
<script>
    function confirmDelete(id) {
        if (confirm('Apakah Anda yakin ingin menghapus admin ini?')) {
            window.location.href = 'daftar_admin.php?action=delete&id=' + id;
        }
    }
</script>

<?php require_once 'includes/footer.php'; ?>
