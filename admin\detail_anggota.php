<?php
require_once 'includes/header.php';

// Cek ID anggota
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'ID anggota tidak valid!');
    redirect('anggota.php');
}

$id_anggota = clean($_GET['id']);

// Ambil data anggota
$sql = "SELECT * FROM anggota WHERE id_anggota = $id_anggota";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Anggota tidak ditemukan!');
    redirect('anggota.php');
}

$anggota = fetch_assoc($result);

// Ambil data peminjaman anggota
$sql_peminjaman = "SELECT p.*, b.judul 
                  FROM peminjaman p
                  JOIN buku b ON p.id_buku = b.id_buku
                  WHERE p.id_anggota = $id_anggota
                  ORDER BY p.created_at DESC";
$result_peminjaman = query($sql_peminjaman);
$peminjaman = fetch_all($result_peminjaman);
?>

<!-- Detail Anggota Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Detail Anggota</h6>
            <div>
                <a href="edit_anggota.php?id=<?php echo $anggota['id_anggota']; ?>" class="btn btn-sm btn-warning">
                    <i class="fas fa-edit"></i> Edit
                </a>
                <a href="anggota.php" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table">
                        <tr>
                            <th width="30%">ID Anggota</th>
                            <td><?php echo $anggota['id_anggota']; ?></td>
                        </tr>
                        <tr>
                            <th>Username</th>
                            <td><?php echo $anggota['username']; ?></td>
                        </tr>
                        <tr>
                            <th>Nama Lengkap</th>
                            <td><?php echo $anggota['nama_lengkap']; ?></td>
                        </tr>
                        <tr>
                            <th>Email</th>
                            <td><?php echo !empty($anggota['email']) ? $anggota['email'] : '-'; ?></td>
                        </tr>
                        <tr>
                            <th>No. Telepon</th>
                            <td><?php echo !empty($anggota['no_telp']) ? $anggota['no_telp'] : '-'; ?></td>
                        </tr>
                        <tr>
                            <th>Alamat</th>
                            <td><?php echo !empty($anggota['alamat']) ? nl2br($anggota['alamat']) : '-'; ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <span class="badge bg-<?php echo $anggota['status'] == 'aktif' ? 'success' : 'danger'; ?>">
                                    <?php echo $anggota['status']; ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Tanggal Daftar</th>
                            <td><?php echo date('d/m/Y H:i', strtotime($anggota['created_at'])); ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold">Statistik Peminjaman</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            // Hitung statistik peminjaman
                            $total_peminjaman = count($peminjaman);
                            
                            $status_count = [
                                'menunggu' => 0,
                                'dipinjam' => 0,
                                'dikembalikan' => 0,
                                'terlambat' => 0,
                                'ditolak' => 0
                            ];
                            
                            foreach ($peminjaman as $p) {
                                $status_count[$p['status_peminjaman']]++;
                            }
                            ?>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body py-2">
                                            <div class="text-center">
                                                <h5 class="mb-0"><?php echo $total_peminjaman; ?></h5>
                                                <small>Total Peminjaman</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body py-2">
                                            <div class="text-center">
                                                <h5 class="mb-0"><?php echo $status_count['dikembalikan']; ?></h5>
                                                <small>Dikembalikan</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body py-2">
                                            <div class="text-center">
                                                <h5 class="mb-0"><?php echo $status_count['dipinjam']; ?></h5>
                                                <small>Sedang Dipinjam</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body py-2">
                                            <div class="text-center">
                                                <h5 class="mb-0"><?php echo $status_count['terlambat']; ?></h5>
                                                <small>Terlambat</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Riwayat Peminjaman -->
            <div class="mt-4">
                <h5 class="font-weight-bold">Riwayat Peminjaman</h5>
                <div class="table-responsive">
                    <table class="table table-bordered datatable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Buku</th>
                                <th>Tanggal Pinjam</th>
                                <th>Tanggal Kembali</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (count($peminjaman) > 0) {
                                foreach ($peminjaman as $p) {
                                    // Set warna status
                                    $status_class = '';
                                    switch ($p['status_peminjaman']) {
                                        case 'menunggu':
                                            $status_class = 'warning';
                                            break;
                                        case 'dipinjam':
                                            $status_class = 'primary';
                                            break;
                                        case 'dikembalikan':
                                            $status_class = 'success';
                                            break;
                                        case 'terlambat':
                                            $status_class = 'danger';
                                            break;
                                        case 'ditolak':
                                            $status_class = 'secondary';
                                            break;
                                    }
                                    
                                    echo "<tr>
                                            <td>{$p['id_peminjaman']}</td>
                                            <td>{$p['judul']}</td>
                                            <td>" . format_tanggal($p['tanggal_pinjam']) . "</td>
                                            <td>" . format_tanggal($p['tanggal_kembali']) . "</td>
                                            <td><span class='badge bg-{$status_class}'>{$p['status_peminjaman']}</span></td>
                                            <td>
                                                <a href='detail_peminjaman.php?id={$p['id_peminjaman']}' class='btn btn-sm btn-info'>
                                                    <i class='fas fa-eye'></i>
                                                </a>
                                            </td>
                                        </tr>";
                                }
                            } else {
                                echo "<tr><td colspan='6' class='text-center'>Tidak ada riwayat peminjaman</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
