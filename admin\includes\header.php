<?php
session_start();
require_once '../config/functions.php';

// Cek apakah admin sudah login
if (!is_admin_logged_in()) {
    redirect('login.php');
}

// Ambil data admin yang sedang login
$admin = get_current_admin();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Sistem Perpustakaan</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        :root {
            --sidebar-width: 250px;
        }
        body {
            font-family: 'Nunito', sans-serif;
            background-color: #f8f9fc;
        }
        .sidebar {
            width: var(--sidebar-width);
            background-color: #4e73df;
            background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 100;
            padding-top: 20px;
            transition: all 0.3s;
        }
        .sidebar-brand {
            color: white;
            text-align: center;
            padding: 15px 0;
            margin-bottom: 20px;
        }
        .sidebar-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.15);
            margin: 15px 0;
        }
        .nav-item {
            margin-bottom: 5px;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .nav-link.active {
            color: white;
            font-weight: bold;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }
        .topbar {
            background-color: white;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            padding: 15px 30px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
            border: none;
            border-radius: 0.35rem;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .dropdown-menu {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: none;
        }
        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }
        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2e59d9;
        }
        .btn-success {
            background-color: #1cc88a;
            border-color: #1cc88a;
        }
        .btn-success:hover {
            background-color: #17a673;
            border-color: #17a673;
        }
        .btn-warning {
            background-color: #f6c23e;
            border-color: #f6c23e;
        }
        .btn-warning:hover {
            background-color: #f4b619;
            border-color: #f4b619;
        }
        .btn-danger {
            background-color: #e74a3b;
            border-color: #e74a3b;
        }
        .btn-danger:hover {
            background-color: #e02d1b;
            border-color: #e02d1b;
        }
        .dashboard-card {
            border-left: 4px solid;
            border-radius: 0.35rem;
        }
        .dashboard-card.primary {
            border-left-color: #4e73df;
        }
        .dashboard-card.success {
            border-left-color: #1cc88a;
        }
        .dashboard-card.warning {
            border-left-color: #f6c23e;
        }
        .dashboard-card.danger {
            border-left-color: #e74a3b;
        }
        .dashboard-card.info {
            border-left-color: #36b9cc;
        }
        .dashboard-card.secondary {
            border-left-color: #858796;
        }
        .dashboard-card.purple {
            border-left-color: #6f42c1;
        }
        .dashboard-card.teal {
            border-left-color: #20c9a6;
        }
        .text-purple {
            color: #6f42c1 !important;
        }
        .text-teal {
            color: #20c9a6 !important;
        }
        .chart-pie, .chart-bar {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4 class="mb-0"><i class="fas fa-book-reader me-2"></i>PERPUSTAKAAN</h4>
            <small>Admin Panel</small>
        </div>
        <hr class="sidebar-divider">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'buku.php' ? 'active' : ''; ?>" href="buku.php">
                    <i class="fas fa-fw fa-book"></i>
                    Manajemen Buku
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'kategori.php' ? 'active' : ''; ?>" href="kategori.php">
                    <i class="fas fa-fw fa-tags"></i>
                    Kategori Buku
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'anggota.php' ? 'active' : ''; ?>" href="anggota.php">
                    <i class="fas fa-fw fa-users"></i>
                    Manajemen Anggota
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'peminjaman.php' ? 'active' : ''; ?>" href="peminjaman.php">
                    <i class="fas fa-fw fa-clipboard-list"></i>
                    Peminjaman
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'laporan.php' ? 'active' : ''; ?>" href="laporan.php">
                    <i class="fas fa-fw fa-chart-bar"></i>
                    Laporan
                </a>
            </li>
            <?php if ($admin['role'] === 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'daftar_admin.php' || basename($_SERVER['PHP_SELF']) == 'tambah_admin.php' || basename($_SERVER['PHP_SELF']) == 'edit_admin.php' ? 'active' : ''; ?>" href="daftar_admin.php">
                    <i class="fas fa-fw fa-user-shield"></i>
                    Manajemen Admin
                </a>
            </li>
            <?php endif; ?>
            <hr class="sidebar-divider">
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-fw fa-sign-out-alt"></i>
                    Logout
                </a>
            </li>
        </ul>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Topbar -->
        <div class="topbar">
            <h4 class="mb-0">
                <?php
                $page_title = '';
                $current_page = basename($_SERVER['PHP_SELF']);

                switch ($current_page) {
                    case 'index.php':
                        $page_title = 'Dashboard';
                        break;
                    case 'buku.php':
                        $page_title = 'Manajemen Buku';
                        break;
                    case 'kategori.php':
                        $page_title = 'Kategori Buku';
                        break;
                    case 'anggota.php':
                        $page_title = 'Manajemen Anggota';
                        break;
                    case 'peminjaman.php':
                        $page_title = 'Peminjaman';
                        break;
                    case 'laporan.php':
                        $page_title = 'Laporan';
                        break;
                    case 'daftar_admin.php':
                        $page_title = 'Manajemen Admin';
                        break;
                    case 'tambah_admin.php':
                        $page_title = 'Tambah Admin';
                        break;
                    case 'edit_admin.php':
                        $page_title = 'Edit Admin';
                        break;
                    default:
                        $page_title = 'Admin Panel';
                }

                echo $page_title;
                ?>
            </h4>
            <div class="dropdown">
                <a class="btn btn-light dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user-circle me-1"></i> <?php echo $admin['nama_admin']; ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item" href="profil.php"><i class="fas fa-user-cog me-2"></i>Profil</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>

        <?php display_flash_message(); ?>
