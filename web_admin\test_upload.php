<?php
require_once 'config.php';

echo "<h2>Test Upload Multi-Folder</h2>";

// Tampilkan path yang akan digunakan
$base_path = dirname(__DIR__);
$upload_dirs = [
    __DIR__ . "/uploads/",                    // web_admin/uploads/
    $base_path . "/api/uploads/books/",       // api/uploads/books/
    $base_path . "/uploads/books/"            // uploads/books/
];

echo "<h3>Path yang akan digunakan:</h3>";
echo "<ul>";
foreach ($upload_dirs as $i => $dir) {
    $exists = file_exists($dir) ? "✅ EXISTS" : "❌ NOT EXISTS";
    $writable = is_writable($dir) ? "✅ WRITABLE" : "❌ NOT WRITABLE";
    echo "<li><strong>$dir</strong><br>$exists | $writable</li>";
}
echo "</ul>";

// Buat folder jika belum ada
echo "<h3>Membuat folder yang belum ada:</h3>";
foreach ($upload_dirs as $dir) {
    if (!file_exists($dir)) {
        $created = mkdir($dir, 0777, true);
        echo "<p>📁 Membuat folder: $dir - " . ($created ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
    } else {
        echo "<p>📁 Folder sudah ada: $dir</p>";
    }
}

// Test upload jika ada file
if ($_POST && isset($_FILES['test_image'])) {
    echo "<h3>Hasil Upload:</h3>";
    $result = uploadImageMultiFolder($_FILES['test_image']);

    if ($result['success']) {
        echo "<div style='color: green;'>";
        echo "<p>✅ " . $result['message'] . "</p>";
        echo "<p><strong>Filename:</strong> " . $result['filename'] . "</p>";
        echo "<p><strong>Uploaded to:</strong></p>";
        echo "<ul>";
        foreach ($result['paths'] as $path) {
            echo "<li>$path</li>";
        }
        echo "</ul>";
        echo "</div>";

        // Tampilkan gambar
        echo "<h4>Preview:</h4>";
        echo "<img src='uploads/" . $result['filename'] . "' style='max-width: 200px; border: 1px solid #ddd; padding: 5px;'>";
    } else {
        echo "<div style='color: red;'>";
        echo "<p>❌ " . $result['message'] . "</p>";
        echo "</div>";
    }
}

// Cek isi folder
echo "<h3>Isi Folder:</h3>";
foreach ($upload_dirs as $dir) {
    echo "<h4>$dir</h4>";
    if (file_exists($dir)) {
        $files = scandir($dir);
        $image_files = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
        });

        if (empty($image_files)) {
            echo "<p style='color: #666;'>Folder kosong</p>";
        } else {
            echo "<ul>";
            foreach ($image_files as $file) {
                echo "<li>$file</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>Folder tidak ada</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-container { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="form-container">
        <h3>Test Upload Gambar</h3>
        <form method="POST" enctype="multipart/form-data">
            <p>
                <label>Pilih gambar:</label><br>
                <input type="file" name="test_image" accept="image/*" required>
            </p>
            <p>
                <button type="submit">Upload Test</button>
            </p>
        </form>
    </div>

    <p><a href="add_book.php">← Kembali ke Add Book</a></p>
</body>
</html>
