<?php
require_once 'config.php';
checkAdminLogin();

$message = '';
$message_type = '';

if ($_POST) {
    $title = trim($_POST['title']);
    $author = trim($_POST['author']);
    $publisher = trim($_POST['publisher']);
    $year = (int)$_POST['year'];
    $category = trim($_POST['category']);
    $description = trim($_POST['description']);
    $stock = (int)$_POST['stock'];
    
    $image_filename = '';
    
    // Proses upload gambar jika ada
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $upload_result = uploadImageMultiFolder($_FILES['image']);
        
        if ($upload_result['success']) {
            $image_filename = $upload_result['filename'];
            $message = $upload_result['message'] . '<br>';
        } else {
            $message = 'Error upload gambar: ' . $upload_result['message'];
            $message_type = 'danger';
        }
    }
    
    // Simpan ke database jika tidak ada error upload
    if ($message_type !== 'danger') {
        try {
            $stmt = $pdo->prepare("INSERT INTO books (title, author, publisher, year, category, description, stock, image) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$title, $author, $publisher, $year, $category, $description, $stock, $image_filename]);
            
            $message .= 'Buku berhasil ditambahkan!';
            $message_type = 'success';
            
            // Reset form
            $_POST = [];
        } catch (PDOException $e) {
            $message = 'Error database: ' . $e->getMessage();
            $message_type = 'danger';
            
            // Hapus gambar yang sudah diupload jika ada error database
            if ($image_filename) {
                deleteImageMultiFolder($image_filename);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tambah Buku - Admin Perpustakaan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.2rem 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            margin-top: 10px;
        }
        .image-preview img {
            max-width: 100%;
            max-height: 180px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3 text-white">
                        <h4><i class="fas fa-book"></i> Perpustakaan</h4>
                        <small>Admin Panel</small>
                    </div>
                    <nav class="nav flex-column px-3">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link" href="books.php">
                            <i class="fas fa-book"></i> Kelola Buku
                        </a>
                        <a class="nav-link active" href="add_book.php">
                            <i class="fas fa-plus"></i> Tambah Buku
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users"></i> Kelola User
                        </a>
                        <a class="nav-link" href="borrows.php">
                            <i class="fas fa-exchange-alt"></i> Peminjaman
                        </a>
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-plus"></i> Tambah Buku Baru</h2>
                        <a href="books.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                    
                    <!-- Alert Message -->
                    <?php if ($message): ?>
                        <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
                            <?= $message ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Form Tambah Buku -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-edit"></i> Form Tambah Buku</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Judul Buku *</label>
                                            <input type="text" class="form-control" id="title" name="title" 
                                                   value="<?= isset($_POST['title']) ? htmlspecialchars($_POST['title']) : '' ?>" required>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="author" class="form-label">Penulis *</label>
                                                    <input type="text" class="form-control" id="author" name="author" 
                                                           value="<?= isset($_POST['author']) ? htmlspecialchars($_POST['author']) : '' ?>" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="publisher" class="form-label">Penerbit</label>
                                                    <input type="text" class="form-control" id="publisher" name="publisher" 
                                                           value="<?= isset($_POST['publisher']) ? htmlspecialchars($_POST['publisher']) : '' ?>">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="year" class="form-label">Tahun Terbit</label>
                                                    <input type="number" class="form-control" id="year" name="year" 
                                                           min="1900" max="<?= date('Y') ?>" 
                                                           value="<?= isset($_POST['year']) ? $_POST['year'] : date('Y') ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category" class="form-label">Kategori *</label>
                                                    <select class="form-control" id="category" name="category" required>
                                                        <option value="">Pilih Kategori</option>
                                                        <option value="Teknologi" <?= (isset($_POST['category']) && $_POST['category'] == 'Teknologi') ? 'selected' : '' ?>>Teknologi</option>
                                                        <option value="Fiksi" <?= (isset($_POST['category']) && $_POST['category'] == 'Fiksi') ? 'selected' : '' ?>>Fiksi</option>
                                                        <option value="Non-Fiksi" <?= (isset($_POST['category']) && $_POST['category'] == 'Non-Fiksi') ? 'selected' : '' ?>>Non-Fiksi</option>
                                                        <option value="Sejarah" <?= (isset($_POST['category']) && $_POST['category'] == 'Sejarah') ? 'selected' : '' ?>>Sejarah</option>
                                                        <option value="Sains" <?= (isset($_POST['category']) && $_POST['category'] == 'Sains') ? 'selected' : '' ?>>Sains</option>
                                                        <option value="Pendidikan" <?= (isset($_POST['category']) && $_POST['category'] == 'Pendidikan') ? 'selected' : '' ?>>Pendidikan</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="stock" class="form-label">Jumlah Stok *</label>
                                            <input type="number" class="form-control" id="stock" name="stock" 
                                                   min="0" value="<?= isset($_POST['stock']) ? $_POST['stock'] : '1' ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">Deskripsi</label>
                                            <textarea class="form-control" id="description" name="description" rows="4"><?= isset($_POST['description']) ? htmlspecialchars($_POST['description']) : '' ?></textarea>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="image" class="form-label">Gambar Sampul</label>
                                            <input type="file" class="form-control" id="image" name="image" 
                                                   accept="image/*" onchange="previewImage(this)">
                                            <small class="text-muted">Format: JPG, PNG, GIF. Maksimal 5MB</small>
                                        </div>
                                        
                                        <div class="image-preview" id="imagePreview">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                            <p class="text-muted mt-2">Preview gambar akan muncul di sini</p>
                                        </div>
                                        
                                        <div class="alert alert-info mt-3">
                                            <small>
                                                <i class="fas fa-info-circle"></i>
                                                <strong>Info:</strong> Gambar akan otomatis disimpan ke:
                                                <ul class="mb-0 mt-1">
                                                    <li>Folder web admin</li>
                                                    <li>Folder API</li>
                                                    <li>Folder backup</li>
                                                </ul>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr>
                                <div class="d-flex justify-content-end">
                                    <button type="reset" class="btn btn-secondary me-2">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Simpan Buku
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    preview.innerHTML = '<img src="' + e.target.result + '" alt="Preview">';
                }
                
                reader.readAsDataURL(input.files[0]);
            } else {
                preview.innerHTML = '<i class="fas fa-image fa-3x text-muted"></i><p class="text-muted mt-2">Preview gambar akan muncul di sini</p>';
            }
        }
    </script>
</body>
</html>
