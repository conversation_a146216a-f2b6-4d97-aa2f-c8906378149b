<?php
// Header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');

// Memuat file koneksi database
require_once '../config/database.php';

// Cek metode request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Metode tidak diizinkan'
    ]);
    exit;
}

// Ambil data dari request
$username = isset($_POST['username']) ? clean($_POST['username']) : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';

// Validasi input
if (empty($username) || empty($password)) {
    echo json_encode([
        'success' => false,
        'message' => 'Username dan password harus diisi'
    ]);
    exit;
}

// Cek user di database
$sql = "SELECT * FROM anggota WHERE username = '$username' AND status = 'aktif'";
$result = query($sql);

if (num_rows($result) === 1) {
    $user = fetch_assoc($result);
    
    // Verifikasi password
    if (password_verify($password, $user['password'])) {
        // Password benar, kirim data user
        echo json_encode([
            'success' => true,
            'message' => 'Login berhasil',
            'data' => [
                'id_anggota' => $user['id_anggota'],
                'username' => $user['username'],
                'nama_lengkap' => $user['nama_lengkap'],
                'email' => $user['email'],
                'no_telp' => $user['no_telp'],
                'alamat' => $user['alamat']
            ]
        ]);
    } else {
        // Password salah
        echo json_encode([
            'success' => false,
            'message' => 'Username atau password salah'
        ]);
    }
} else {
    // User tidak ditemukan
    echo json_encode([
        'success' => false,
        'message' => 'Username atau password salah'
    ]);
}
?>
