<?php
require_once 'includes/header.php';

// Cek ID buku
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'ID buku tidak valid!');
    redirect('buku.php');
}

$id_buku = clean($_GET['id']);

// Ambil data buku
$sql = "SELECT b.*, k.nama_kategori 
        FROM buku b
        LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
        WHERE b.id_buku = $id_buku";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Buku tidak ditemukan!');
    redirect('buku.php');
}

$buku = fetch_assoc($result);

// Ambil data peminjaman buku
$sql_peminjaman = "SELECT p.*, a.nama_lengkap 
                  FROM peminjaman p
                  JOIN anggota a ON p.id_anggota = a.id_anggota
                  WHERE p.id_buku = $id_buku
                  ORDER BY p.created_at DESC
                  LIMIT 10";
$result_peminjaman = query($sql_peminjaman);
$peminjaman = fetch_all($result_peminjaman);
?>

<!-- Detail Buku Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Detail Buku</h6>
            <div>
                <a href="edit_buku.php?id=<?php echo $buku['id_buku']; ?>" class="btn btn-sm btn-warning">
                    <i class="fas fa-edit"></i> Edit
                </a>
                <a href="buku.php" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <?php if (!empty($buku['gambar_sampul']) && file_exists('../' . $buku['gambar_sampul'])): ?>
                        <img src="../<?php echo $buku['gambar_sampul']; ?>" alt="Sampul Buku" class="img-fluid rounded" style="max-height: 300px;">
                    <?php else: ?>
                        <img src="https://via.placeholder.com/300x400?text=No+Cover" alt="No Cover" class="img-fluid rounded">
                    <?php endif; ?>
                </div>
                <div class="col-md-8">
                    <h4 class="font-weight-bold"><?php echo $buku['judul']; ?></h4>
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Penulis:</strong> <?php echo $buku['penulis']; ?></p>
                            <p><strong>Penerbit:</strong> <?php echo !empty($buku['penerbit']) ? $buku['penerbit'] : '-'; ?></p>
                            <p><strong>Tahun Terbit:</strong> <?php echo !empty($buku['tahun_terbit']) ? $buku['tahun_terbit'] : '-'; ?></p>
                            <p><strong>ISBN:</strong> <?php echo !empty($buku['isbn']) ? $buku['isbn'] : '-'; ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Kategori:</strong> <?php echo !empty($buku['nama_kategori']) ? $buku['nama_kategori'] : '-'; ?></p>
                            <p><strong>Stok:</strong> <span class="badge bg-<?php echo ($buku['jumlah_stok'] > 0) ? 'success' : 'danger'; ?>"><?php echo $buku['jumlah_stok']; ?></span></p>
                            <p><strong>Tanggal Ditambahkan:</strong> <?php echo date('d/m/Y H:i', strtotime($buku['created_at'])); ?></p>
                            <p><strong>Terakhir Diperbarui:</strong> <?php echo date('d/m/Y H:i', strtotime($buku['updated_at'])); ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($buku['deskripsi'])): ?>
                    <div class="mt-3">
                        <h5>Deskripsi:</h5>
                        <p><?php echo nl2br($buku['deskripsi']); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Riwayat Peminjaman -->
            <div class="mt-4">
                <h5 class="font-weight-bold">Riwayat Peminjaman</h5>
                <div class="table-responsive">
                    <table class="table table-bordered datatable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Anggota</th>
                                <th>Tanggal Pinjam</th>
                                <th>Tanggal Kembali</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (count($peminjaman) > 0) {
                                foreach ($peminjaman as $p) {
                                    // Set warna status
                                    $status_class = '';
                                    switch ($p['status_peminjaman']) {
                                        case 'menunggu':
                                            $status_class = 'warning';
                                            break;
                                        case 'dipinjam':
                                            $status_class = 'primary';
                                            break;
                                        case 'dikembalikan':
                                            $status_class = 'success';
                                            break;
                                        case 'terlambat':
                                            $status_class = 'danger';
                                            break;
                                        case 'ditolak':
                                            $status_class = 'secondary';
                                            break;
                                    }
                                    
                                    echo "<tr>
                                            <td>{$p['id_peminjaman']}</td>
                                            <td>{$p['nama_lengkap']}</td>
                                            <td>" . format_tanggal($p['tanggal_pinjam']) . "</td>
                                            <td>" . format_tanggal($p['tanggal_kembali']) . "</td>
                                            <td><span class='badge bg-{$status_class}'>{$p['status_peminjaman']}</span></td>
                                            <td>
                                                <a href='detail_peminjaman.php?id={$p['id_peminjaman']}' class='btn btn-sm btn-info'>
                                                    <i class='fas fa-eye'></i>
                                                </a>
                                            </td>
                                        </tr>";
                                }
                            } else {
                                echo "<tr><td colspan='6' class='text-center'>Tidak ada riwayat peminjaman</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
