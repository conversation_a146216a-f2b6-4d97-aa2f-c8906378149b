<?php
require_once 'includes/header.php';

// Proses ubah status anggota
if (isset($_GET['action']) && $_GET['action'] == 'status' && isset($_GET['id']) && isset($_GET['status'])) {
    $id_anggota = clean($_GET['id']);
    $status = clean($_GET['status']);
    
    if ($status == 'aktif' || $status == 'nonaktif') {
        $sql_update = "UPDATE anggota SET status = '$status' WHERE id_anggota = $id_anggota";
        $result_update = query($sql_update);
        
        if ($result_update) {
            set_flash_message('success', 'Status anggota berhasil diubah!');
        } else {
            set_flash_message('danger', 'Gagal mengubah status anggota!');
        }
    } else {
        set_flash_message('danger', 'Status tidak valid!');
    }
    
    redirect('anggota.php');
}

// Proses hapus anggota
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id_anggota = clean($_GET['id']);
    
    // Cek apakah anggota memiliki peminjaman aktif
    $sql_cek = "SELECT * FROM peminjaman WHERE id_anggota = $id_anggota AND status_peminjaman IN ('menunggu', 'dipinjam', 'terlambat')";
    $result_cek = query($sql_cek);
    
    if (num_rows($result_cek) > 0) {
        set_flash_message('danger', 'Anggota tidak dapat dihapus karena memiliki peminjaman aktif!');
    } else {
        // Hapus anggota
        $sql_delete = "DELETE FROM anggota WHERE id_anggota = $id_anggota";
        $result_delete = query($sql_delete);
        
        if ($result_delete) {
            set_flash_message('success', 'Anggota berhasil dihapus!');
        } else {
            set_flash_message('danger', 'Gagal menghapus anggota!');
        }
    }
    
    redirect('anggota.php');
}
?>

<!-- Manajemen Anggota Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Anggota</h6>
            <a href="tambah_anggota.php" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Tambah Anggota
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Nama Lengkap</th>
                            <th>Email</th>
                            <th>No. Telp</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Ambil data anggota
                        $sql = "SELECT * FROM anggota ORDER BY id_anggota DESC";
                        $result = query($sql);
                        $anggota = fetch_all($result);
                        
                        if (count($anggota) > 0) {
                            foreach ($anggota as $a) {
                                $status_badge = ($a['status'] == 'aktif') ? 'success' : 'danger';
                                $toggle_status = ($a['status'] == 'aktif') ? 'nonaktif' : 'aktif';
                                $toggle_text = ($a['status'] == 'aktif') ? 'Nonaktifkan' : 'Aktifkan';
                                $toggle_icon = ($a['status'] == 'aktif') ? 'fa-user-times' : 'fa-user-check';
                                
                                echo "<tr>
                                        <td>{$a['id_anggota']}</td>
                                        <td>{$a['username']}</td>
                                        <td>{$a['nama_lengkap']}</td>
                                        <td>{$a['email']}</td>
                                        <td>{$a['no_telp']}</td>
                                        <td><span class='badge bg-{$status_badge}'>{$a['status']}</span></td>
                                        <td>
                                            <a href='detail_anggota.php?id={$a['id_anggota']}' class='btn btn-sm btn-info'>
                                                <i class='fas fa-eye'></i>
                                            </a>
                                            <a href='edit_anggota.php?id={$a['id_anggota']}' class='btn btn-sm btn-warning'>
                                                <i class='fas fa-edit'></i>
                                            </a>
                                            <a href='anggota.php?action=status&id={$a['id_anggota']}&status={$toggle_status}' class='btn btn-sm btn-" . ($a['status'] == 'aktif' ? 'danger' : 'success') . "'>
                                                <i class='fas {$toggle_icon}'></i>
                                            </a>
                                            <a href='javascript:void(0);' onclick='confirmDelete({$a['id_anggota']})' class='btn btn-sm btn-danger'>
                                                <i class='fas fa-trash'></i>
                                            </a>
                                        </td>
                                    </tr>";
                            }
                        } else {
                            echo "<tr><td colspan='7' class='text-center'>Tidak ada data anggota</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Script untuk konfirmasi hapus -->
<script>
    function confirmDelete(id) {
        if (confirm('Apakah Anda yakin ingin menghapus anggota ini?')) {
            window.location.href = 'anggota.php?action=delete&id=' + id;
        }
    }
</script>

<?php require_once 'includes/footer.php'; ?>
