<?php
require_once 'includes/header.php';

// Ambil data admin yang sedang login
$id_admin = $_SESSION['admin_id'];
$sql = "SELECT * FROM admin WHERE id_admin = $id_admin";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Data admin tidak ditemukan!');
    redirect('index.php');
}

$admin_data = fetch_assoc($result);

// Proses update profil
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = clean($_POST['username']);
    $nama_admin = clean($_POST['nama_admin']);
    $email = clean($_POST['email']);
    $no_telp = clean($_POST['no_telp']);
    $password_lama = $_POST['password_lama'];
    $password_baru = $_POST['password_baru'];
    $konfirmasi_password = $_POST['konfirmasi_password'];
    
    // Validasi input
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'Username harus diisi!';
    }
    
    if (empty($nama_admin)) {
        $errors[] = 'Nama admin harus diisi!';
    }
    
    // Cek apakah username sudah digunakan oleh admin lain
    $sql_check = "SELECT * FROM admin WHERE username = '$username' AND id_admin != $id_admin";
    $result_check = query($sql_check);
    
    if (num_rows($result_check) > 0) {
        $errors[] = 'Username sudah digunakan oleh admin lain!';
    }
    
    // Jika password lama diisi, validasi password
    if (!empty($password_lama)) {
        // Verifikasi password lama
        if (!password_verify($password_lama, $admin_data['password'])) {
            $errors[] = 'Password lama tidak sesuai!';
        }
        
        // Validasi password baru
        if (empty($password_baru)) {
            $errors[] = 'Password baru harus diisi!';
        }
        
        if ($password_baru !== $konfirmasi_password) {
            $errors[] = 'Konfirmasi password tidak cocok!';
        }
    }
    
    // Jika tidak ada error, update data admin
    if (empty($errors)) {
        // Jika password diisi, update password juga
        if (!empty($password_lama) && !empty($password_baru)) {
            // Hash password baru
            $hashed_password = password_hash($password_baru, PASSWORD_DEFAULT);
            
            // Update data admin dengan password baru
            $sql_update = "UPDATE admin SET 
                          username = '$username', 
                          password = '$hashed_password', 
                          nama_admin = '$nama_admin', 
                          email = '$email', 
                          no_telp = '$no_telp'
                          WHERE id_admin = $id_admin";
        } else {
            // Update data admin tanpa mengubah password
            $sql_update = "UPDATE admin SET 
                          username = '$username', 
                          nama_admin = '$nama_admin', 
                          email = '$email', 
                          no_telp = '$no_telp'
                          WHERE id_admin = $id_admin";
        }
        
        $result_update = query($sql_update);
        
        if ($result_update) {
            // Update session
            $_SESSION['admin_username'] = $username;
            $_SESSION['admin_nama'] = $nama_admin;
            
            set_flash_message('success', 'Profil berhasil diperbarui!');
            redirect('profil.php');
        } else {
            $errors[] = 'Gagal memperbarui profil!';
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}
?>

<!-- Profil Content -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Profil</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-5x text-gray-300"></i>
                    </div>
                    <h5 class="font-weight-bold"><?php echo $admin_data['nama_admin']; ?></h5>
                    <p class="text-muted"><?php echo $admin_data['username']; ?></p>
                    <hr>
                    <div class="text-left">
                        <p><i class="fas fa-envelope me-2"></i> <?php echo !empty($admin_data['email']) ? $admin_data['email'] : '-'; ?></p>
                        <p><i class="fas fa-phone me-2"></i> <?php echo !empty($admin_data['no_telp']) ? $admin_data['no_telp'] : '-'; ?></p>
                        <p><i class="fas fa-clock me-2"></i> Terdaftar sejak: <?php echo date('d/m/Y', strtotime($admin_data['created_at'])); ?></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Edit Profil</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo $admin_data['username']; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="nama_admin" class="form-label">Nama Admin <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_admin" name="nama_admin" value="<?php echo $admin_data['nama_admin']; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $admin_data['email']; ?>">
                        </div>
                        <div class="mb-3">
                            <label for="no_telp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="no_telp" name="no_telp" value="<?php echo $admin_data['no_telp']; ?>">
                        </div>
                        
                        <hr>
                        <h6 class="font-weight-bold">Ubah Password</h6>
                        <p class="text-muted small">Kosongkan jika tidak ingin mengubah password</p>
                        
                        <div class="mb-3">
                            <label for="password_lama" class="form-label">Password Lama</label>
                            <input type="password" class="form-control" id="password_lama" name="password_lama">
                        </div>
                        <div class="mb-3">
                            <label for="password_baru" class="form-label">Password Baru</label>
                            <input type="password" class="form-control" id="password_baru" name="password_baru">
                        </div>
                        <div class="mb-3">
                            <label for="konfirmasi_password" class="form-label">Konfirmasi Password Baru</label>
                            <input type="password" class="form-control" id="konfirmasi_password" name="konfirmasi_password">
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
