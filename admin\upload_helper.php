<?php
// Helper function untuk upload gambar dengan sinkronisasi multi-folder
function uploadImageMultiSync($file, $prefix = 'book_') {
    // Validasi file
    $allowed_types = ['image/jpeg', 'image/png', 'image/jpg'];
    $max_size = 2 * 1024 * 1024; // 2MB
    
    $file_name = $file['name'];
    $file_size = $file['size'];
    $file_tmp = $file['tmp_name'];
    $file_type = $file['type'];
    
    // Cek tipe file
    if (!in_array($file_type, $allowed_types)) {
        return ['success' => false, 'message' => 'Tipe file tidak didukung. Hanya JPG, JPEG, dan PNG yang diperbolehkan!'];
    }
    
    // Cek ukuran file
    if ($file_size > $max_size) {
        return ['success' => false, 'message' => 'Ukuran file terlalu besar. Maksimal 2MB!'];
    }
    
    // Generate nama file unik
    $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);
    $new_file_name = $prefix . time() . '.' . $file_ext;
    
    // Daftar direktori tujuan untuk sinkronisasi
    $upload_dirs = [
        '../uploads/books/',           // Folder utama
        '../api/uploads/books/',       // Folder API
        '../web_admin/uploads/'        // Folder web admin
    ];
    
    // Buat semua direktori jika belum ada
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }
    
    // Upload ke direktori utama terlebih dahulu
    $main_upload_path = $upload_dirs[0] . $new_file_name;
    
    if (move_uploaded_file($file_tmp, $main_upload_path)) {
        $uploaded_paths = [$main_upload_path];
        
        // Copy ke direktori lainnya
        $copy_success = true;
        foreach (array_slice($upload_dirs, 1) as $dir) {
            $target_path = $dir . $new_file_name;
            if (copy($main_upload_path, $target_path)) {
                $uploaded_paths[] = $target_path;
            } else {
                error_log("Gagal copy file ke: " . $target_path);
                $copy_success = false;
            }
        }
        
        // Log hasil
        if ($copy_success) {
            error_log("File berhasil disinkronisasi ke semua folder: " . $new_file_name);
        } else {
            error_log("Beberapa folder gagal disinkronisasi untuk file: " . $new_file_name);
        }
        
        return [
            'success' => true,
            'filename' => $new_file_name,
            'relative_path' => 'uploads/books/' . $new_file_name,
            'uploaded_paths' => $uploaded_paths,
            'message' => 'File berhasil diupload ke ' . count($uploaded_paths) . ' lokasi'
        ];
    } else {
        return ['success' => false, 'message' => 'Gagal mengupload file!'];
    }
}

// Fungsi untuk menghapus gambar dari semua folder
function deleteImageMultiSync($filename) {
    $upload_dirs = [
        '../uploads/books/',
        '../api/uploads/books/',
        '../web_admin/uploads/'
    ];
    
    $deleted_count = 0;
    foreach ($upload_dirs as $dir) {
        $file_path = $dir . $filename;
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                $deleted_count++;
                error_log("Deleted file: " . $file_path);
            } else {
                error_log("Failed to delete file: " . $file_path);
            }
        }
    }
    
    return $deleted_count;
}

// Fungsi untuk cek apakah file gambar ada di semua folder
function checkImageSync($filename) {
    $upload_dirs = [
        '../uploads/books/',
        '../api/uploads/books/',
        '../web_admin/uploads/'
    ];
    
    $status = [];
    foreach ($upload_dirs as $dir) {
        $file_path = $dir . $filename;
        $status[$dir] = file_exists($file_path);
    }
    
    return $status;
}

// Fungsi untuk sinkronisasi ulang file yang sudah ada
function resyncExistingImages() {
    $main_dir = '../uploads/books/';
    $target_dirs = [
        '../api/uploads/books/',
        '../web_admin/uploads/'
    ];
    
    if (!is_dir($main_dir)) {
        return ['success' => false, 'message' => 'Folder utama tidak ditemukan'];
    }
    
    // Buat target directories jika belum ada
    foreach ($target_dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }
    
    $files = glob($main_dir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
    $synced_count = 0;
    $failed_count = 0;
    
    foreach ($files as $file) {
        $filename = basename($file);
        
        foreach ($target_dirs as $target_dir) {
            $target_path = $target_dir . $filename;
            
            if (!file_exists($target_path)) {
                if (copy($file, $target_path)) {
                    $synced_count++;
                } else {
                    $failed_count++;
                    error_log("Failed to sync file: " . $filename . " to " . $target_dir);
                }
            }
        }
    }
    
    return [
        'success' => true,
        'synced' => $synced_count,
        'failed' => $failed_count,
        'message' => "Sinkronisasi selesai. $synced_count file berhasil, $failed_count gagal."
    ];
}
?>
