<?php
require_once 'includes/header.php';

// Proses hapus kategori
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id_kategori = clean($_GET['id']);
    
    // Cek apakah kategori digunakan oleh buku
    $sql_cek = "SELECT COUNT(*) as total FROM buku WHERE id_kategori = $id_kategori";
    $result_cek = query($sql_cek);
    $data_cek = fetch_assoc($result_cek);
    
    if ($data_cek['total'] > 0) {
        set_flash_message('danger', 'Kategori tidak dapat dihapus karena masih digunakan oleh buku!');
    } else {
        // Hapus kategori
        $sql_delete = "DELETE FROM kategori WHERE id_kategori = $id_kategori";
        $result_delete = query($sql_delete);
        
        if ($result_delete) {
            set_flash_message('success', 'Kategori berhasil dihapus!');
        } else {
            set_flash_message('danger', 'Gagal menghapus kategori!');
        }
    }
    
    redirect('kategori.php');
}

// Proses tambah/edit kategori
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama_kategori = clean($_POST['nama_kategori']);
    $deskripsi = clean($_POST['deskripsi']);
    $id_kategori = isset($_POST['id_kategori']) ? clean($_POST['id_kategori']) : null;
    
    // Validasi input
    $errors = [];
    
    if (empty($nama_kategori)) {
        $errors[] = 'Nama kategori harus diisi!';
    }
    
    // Cek apakah nama kategori sudah ada (untuk tambah atau edit)
    if ($id_kategori) {
        // Edit - cek nama kategori kecuali kategori yang sedang diedit
        $sql_check = "SELECT * FROM kategori WHERE nama_kategori = '$nama_kategori' AND id_kategori != $id_kategori";
    } else {
        // Tambah - cek semua nama kategori
        $sql_check = "SELECT * FROM kategori WHERE nama_kategori = '$nama_kategori'";
    }
    
    $result_check = query($sql_check);
    
    if (num_rows($result_check) > 0) {
        $errors[] = 'Nama kategori sudah digunakan!';
    }
    
    // Jika tidak ada error, simpan data kategori
    if (empty($errors)) {
        if ($id_kategori) {
            // Update kategori
            $sql = "UPDATE kategori SET nama_kategori = '$nama_kategori', deskripsi = '$deskripsi' WHERE id_kategori = $id_kategori";
            $success_message = 'Kategori berhasil diperbarui!';
        } else {
            // Tambah kategori baru
            $sql = "INSERT INTO kategori (nama_kategori, deskripsi) VALUES ('$nama_kategori', '$deskripsi')";
            $success_message = 'Kategori berhasil ditambahkan!';
        }
        
        $result = query($sql);
        
        if ($result) {
            set_flash_message('success', $success_message);
            redirect('kategori.php');
        } else {
            $errors[] = 'Gagal menyimpan kategori!';
        }
    }
    
    // Jika ada error, tampilkan pesan error
    if (!empty($errors)) {
        foreach ($errors as $error) {
            set_flash_message('danger', $error);
        }
    }
}

// Ambil data kategori untuk edit jika ada parameter id
$kategori_edit = null;
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])) {
    $id_kategori = clean($_GET['id']);
    $sql_edit = "SELECT * FROM kategori WHERE id_kategori = $id_kategori";
    $result_edit = query($sql_edit);
    
    if (num_rows($result_edit) === 1) {
        $kategori_edit = fetch_assoc($result_edit);
    } else {
        set_flash_message('danger', 'Kategori tidak ditemukan!');
        redirect('kategori.php');
    }
}
?>

<!-- Kategori Content -->
<div class="container-fluid">
    <div class="row">
        <!-- Form Tambah/Edit Kategori -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <?php echo $kategori_edit ? 'Edit Kategori' : 'Tambah Kategori Baru'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <?php if ($kategori_edit): ?>
                            <input type="hidden" name="id_kategori" value="<?php echo $kategori_edit['id_kategori']; ?>">
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <label for="nama_kategori" class="form-label">Nama Kategori <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_kategori" name="nama_kategori" value="<?php echo $kategori_edit ? $kategori_edit['nama_kategori'] : ''; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="deskripsi" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"><?php echo $kategori_edit ? $kategori_edit['deskripsi'] : ''; ?></textarea>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <?php echo $kategori_edit ? 'Update Kategori' : 'Simpan Kategori'; ?>
                            </button>
                            <?php if ($kategori_edit): ?>
                                <a href="kategori.php" class="btn btn-secondary">Batal Edit</a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Daftar Kategori -->
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Daftar Kategori</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered datatable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama Kategori</th>
                                    <th>Deskripsi</th>
                                    <th>Jumlah Buku</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Ambil data kategori
                                $sql = "SELECT k.*, COUNT(b.id_buku) as jumlah_buku 
                                        FROM kategori k
                                        LEFT JOIN buku b ON k.id_kategori = b.id_kategori
                                        GROUP BY k.id_kategori
                                        ORDER BY k.nama_kategori ASC";
                                $result = query($sql);
                                $kategori_list = fetch_all($result);
                                
                                if (count($kategori_list) > 0) {
                                    foreach ($kategori_list as $kategori) {
                                        echo "<tr>
                                                <td>{$kategori['id_kategori']}</td>
                                                <td>{$kategori['nama_kategori']}</td>
                                                <td>" . (empty($kategori['deskripsi']) ? '-' : $kategori['deskripsi']) . "</td>
                                                <td>{$kategori['jumlah_buku']}</td>
                                                <td>
                                                    <a href='kategori.php?action=edit&id={$kategori['id_kategori']}' class='btn btn-sm btn-warning'>
                                                        <i class='fas fa-edit'></i>
                                                    </a>
                                                    <a href='javascript:void(0);' onclick='confirmDelete({$kategori['id_kategori']})' class='btn btn-sm btn-danger " . ($kategori['jumlah_buku'] > 0 ? 'disabled' : '') . "'>
                                                        <i class='fas fa-trash'></i>
                                                    </a>
                                                </td>
                                            </tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='5' class='text-center'>Tidak ada data kategori</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Script untuk konfirmasi hapus -->
<script>
    function confirmDelete(id) {
        if (confirm('Apakah Anda yakin ingin menghapus kategori ini?')) {
            window.location.href = 'kategori.php?action=delete&id=' + id;
        }
    }
</script>

<?php require_once 'includes/footer.php'; ?>
