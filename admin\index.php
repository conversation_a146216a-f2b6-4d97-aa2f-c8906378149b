<?php
require_once 'includes/header.php';

// Menghitung jumlah data
$total_buku = count_books();
$total_anggota = count_members();
$total_peminjaman = count_loans();
$peminjaman_menunggu = count_loans('menunggu');
$peminjaman_terlambat = count_loans('terlambat');

// Mendapatkan statistik tambahan
$books_by_category = get_books_by_category();
$monthly_loans = get_monthly_loans();
$loans_by_status = get_loans_by_status();
$popular_books = get_popular_books();
$active_members = get_active_members();

// Menyiapkan data untuk chart
$category_labels = [];
$category_data = [];
foreach ($books_by_category as $category) {
    $category_labels[] = $category['nama_kategori'];
    $category_data[] = $category['jumlah_buku'];
}

$status_labels = array_keys($loans_by_status);
$status_data = array_values($loans_by_status);
?>

<!-- Dashboard Content -->
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
        <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm" onclick="window.print()">
            <i class="fas fa-download fa-sm text-white-50"></i> Generate Report
        </a>
    </div>

    <!-- Statistik Utama -->
    <div class="row">
        <!-- Total Buku -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Buku</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_buku; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Anggota -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Anggota</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_anggota; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Peminjaman -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Peminjaman</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_peminjaman; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Peminjaman Menunggu -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Peminjaman Menunggu</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $peminjaman_menunggu; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistik Tambahan -->
    <div class="row">
        <!-- Peminjaman Terlambat -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card info h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Peminjaman Terlambat</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $peminjaman_terlambat; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buku Tersedia -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card secondary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                Buku Tersedia</div>
                            <?php
                            $sql = "SELECT SUM(jumlah_stok) as total_stok FROM buku";
                            $result = query($sql);
                            $row = fetch_assoc($result);
                            $total_stok = $row['total_stok'];
                            ?>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_stok; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book-open fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Kategori -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card purple h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-purple text-uppercase mb-1">
                                Total Kategori</div>
                            <?php
                            $sql = "SELECT COUNT(*) as total FROM kategori";
                            $result = query($sql);
                            $row = fetch_assoc($result);
                            $total_kategori = $row['total'];
                            ?>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_kategori; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Peminjaman Bulan Ini -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card teal h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-teal text-uppercase mb-1">
                                Peminjaman Bulan Ini</div>
                            <?php
                            $current_month = date('m');
                            $current_year = date('Y');
                            $sql = "SELECT COUNT(*) as total FROM peminjaman
                                    WHERE MONTH(tanggal_pinjam) = $current_month
                                    AND YEAR(tanggal_pinjam) = $current_year";
                            $result = query($sql);
                            $row = fetch_assoc($result);
                            $peminjaman_bulan_ini = $row['total'];
                            ?>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $peminjaman_bulan_ini; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grafik Statistik -->
    <div class="row">
        <!-- Grafik Buku per Kategori -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Buku per Kategori</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="chartBukuPerKategori"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grafik Peminjaman per Status -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Peminjaman per Status</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="chartPeminjamanPerStatus"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Grafik Peminjaman Bulanan -->
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Peminjaman Bulanan (<?php echo date('Y'); ?>)</h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar">
                        <canvas id="chartPeminjamanBulanan"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Buku Terpopuler -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Buku Terpopuler</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Judul Buku</th>
                                    <th>Total Peminjaman</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($popular_books as $book): ?>
                                <tr>
                                    <td><?php echo $book['judul']; ?></td>
                                    <td><?php echo $book['total_peminjaman']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Anggota Teraktif -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Anggota Teraktif</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Nama Anggota</th>
                                    <th>Total Peminjaman</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($active_members as $member): ?>
                                <tr>
                                    <td><?php echo $member['nama_lengkap']; ?></td>
                                    <td><?php echo $member['total_peminjaman']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Peminjaman Terbaru -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Peminjaman Terbaru</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Anggota</th>
                            <th>Buku</th>
                            <th>Tanggal Pinjam</th>
                            <th>Tanggal Kembali</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Ambil data peminjaman terbaru
                        $sql = "SELECT p.*, a.nama_lengkap, b.judul
                                FROM peminjaman p
                                JOIN anggota a ON p.id_anggota = a.id_anggota
                                JOIN buku b ON p.id_buku = b.id_buku
                                ORDER BY p.created_at DESC
                                LIMIT 10";
                        $result = query($sql);
                        $peminjaman = fetch_all($result);

                        if (count($peminjaman) > 0) {
                            foreach ($peminjaman as $p) {
                                // Set warna status
                                $status_class = '';
                                switch ($p['status_peminjaman']) {
                                    case 'menunggu':
                                        $status_class = 'warning';
                                        break;
                                    case 'dipinjam':
                                        $status_class = 'primary';
                                        break;
                                    case 'dikembalikan':
                                        $status_class = 'success';
                                        break;
                                    case 'terlambat':
                                        $status_class = 'danger';
                                        break;
                                    case 'ditolak':
                                        $status_class = 'secondary';
                                        break;
                                }

                                echo "<tr>
                                        <td>{$p['id_peminjaman']}</td>
                                        <td>{$p['nama_lengkap']}</td>
                                        <td>{$p['judul']}</td>
                                        <td>" . format_tanggal($p['tanggal_pinjam']) . "</td>
                                        <td>" . format_tanggal($p['tanggal_kembali']) . "</td>
                                        <td><span class='badge bg-{$status_class}'>{$p['status_peminjaman']}</span></td>
                                        <td>
                                            <a href='detail_peminjaman.php?id={$p['id_peminjaman']}' class='btn btn-sm btn-info'>
                                                <i class='fas fa-eye'></i>
                                            </a>
                                        </td>
                                    </tr>";
                            }
                        } else {
                            echo "<tr><td colspan='7' class='text-center'>Tidak ada data peminjaman</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Data untuk chart
const categoryLabels = <?php echo json_encode($category_labels); ?>;
const categoryData = <?php echo json_encode($category_data); ?>;
const statusLabels = <?php echo json_encode($status_labels); ?>;
const statusData = <?php echo json_encode($status_data); ?>;
const monthlyLabels = <?php echo json_encode($monthly_loans['months']); ?>;
const monthlyData = <?php echo json_encode($monthly_loans['loans']); ?>;

// Chart Buku per Kategori
const ctxCategory = document.getElementById('chartBukuPerKategori').getContext('2d');
new Chart(ctxCategory, {
    type: 'pie',
    data: {
        labels: categoryLabels,
        datasets: [{
            data: categoryData,
            backgroundColor: [
                '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                '#6f42c1', '#fd7e14', '#20c9a6', '#5a5c69', '#858796'
            ],
            hoverBackgroundColor: [
                '#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b',
                '#6610f2', '#fd7e14', '#17a673', '#4e4f52', '#6e707e'
            ],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return `${context.label}: ${context.raw} buku`;
                    }
                }
            }
        }
    },
});

// Chart Peminjaman per Status
const ctxStatus = document.getElementById('chartPeminjamanPerStatus').getContext('2d');
new Chart(ctxStatus, {
    type: 'doughnut',
    data: {
        labels: statusLabels.map(status => status.charAt(0).toUpperCase() + status.slice(1)),
        datasets: [{
            data: statusData,
            backgroundColor: [
                '#f6c23e', '#4e73df', '#1cc88a', '#e74a3b', '#5a5c69'
            ],
            hoverBackgroundColor: [
                '#f4b619', '#2e59d9', '#17a673', '#e02d1b', '#4e4f52'
            ],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return `${context.label}: ${context.raw} peminjaman`;
                    }
                }
            }
        }
    },
});

// Chart Peminjaman Bulanan
const ctxMonthly = document.getElementById('chartPeminjamanBulanan').getContext('2d');
new Chart(ctxMonthly, {
    type: 'bar',
    data: {
        labels: monthlyLabels,
        datasets: [{
            label: 'Jumlah Peminjaman',
            data: monthlyData,
            backgroundColor: '#4e73df',
            borderColor: '#4e73df',
            borderWidth: 1
        }]
    },
    options: {
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    precision: 0
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
