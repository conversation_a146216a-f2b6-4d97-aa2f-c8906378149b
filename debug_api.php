<!DOCTYPE html>
<html>
<head>
    <title>Debug API Response</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .json-output { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; }
        pre { margin: 0; white-space: pre-wrap; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Debug API Books - Kategori Issue</h1>
    
    <div class="debug-section">
        <h2>1. Test API Books Response</h2>
        <?php
        $api_url = 'http://192.168.218.5/perpus_andro/api/books.php';
        echo "<p><strong>Testing URL:</strong> $api_url</p>";
        
        $response = @file_get_contents($api_url);
        
        if ($response === false) {
            echo "<p class='error'>❌ Gagal mengakses API</p>";
        } else {
            $data = json_decode($response, true);
            
            if ($data && isset($data['success']) && $data['success']) {
                echo "<p class='success'>✅ API Response: SUCCESS</p>";
                echo "<p><strong>Total Books:</strong> " . count($data['data']) . "</p>";
                
                if (!empty($data['data'])) {
                    echo "<h3>Sample Book Data (First Book):</h3>";
                    $first_book = $data['data'][0];
                    
                    echo "<table>";
                    echo "<tr><th>Field</th><th>Value</th><th>Type</th></tr>";
                    foreach ($first_book as $key => $value) {
                        $type = gettype($value);
                        $display_value = is_null($value) ? 'NULL' : htmlspecialchars($value);
                        echo "<tr>";
                        echo "<td><strong>$key</strong></td>";
                        echo "<td>$display_value</td>";
                        echo "<td>$type</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    // Cek khusus field kategori
                    echo "<h3>Kategori Analysis:</h3>";
                    if (isset($first_book['kategori'])) {
                        if (!empty($first_book['kategori']) && $first_book['kategori'] !== null) {
                            echo "<p class='success'>✅ Field 'kategori' ada dan berisi: <strong>" . htmlspecialchars($first_book['kategori']) . "</strong></p>";
                        } else {
                            echo "<p class='warning'>⚠️ Field 'kategori' ada tapi kosong/null</p>";
                        }
                    } else {
                        echo "<p class='error'>❌ Field 'kategori' tidak ada di response</p>";
                    }
                    
                    // Cek semua buku
                    echo "<h3>All Books Kategori Status:</h3>";
                    echo "<table>";
                    echo "<tr><th>Judul</th><th>Kategori</th><th>Status</th></tr>";
                    
                    foreach ($data['data'] as $book) {
                        $kategori_value = isset($book['kategori']) ? $book['kategori'] : 'FIELD_NOT_EXISTS';
                        $status = '';
                        
                        if (!isset($book['kategori'])) {
                            $status = '❌ Field tidak ada';
                        } elseif (empty($book['kategori']) || $book['kategori'] === null) {
                            $status = '⚠️ Kosong/NULL';
                        } else {
                            $status = '✅ Ada data';
                        }
                        
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($book['judul']) . "</td>";
                        echo "<td>" . htmlspecialchars($kategori_value) . "</td>";
                        echo "<td>$status</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<p class='error'>❌ API Response: FAILED</p>";
                if (isset($data['message'])) {
                    echo "<p><strong>Error Message:</strong> " . htmlspecialchars($data['message']) . "</p>";
                }
            }
            
            echo "<h3>Raw JSON Response:</h3>";
            echo "<div class='json-output'><pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre></div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>2. Database Direct Check</h2>
        <?php
        require_once 'config/database.php';
        
        echo "<h3>Cek Tabel Kategori:</h3>";
        $kategori_sql = "SELECT COUNT(*) as total FROM kategori";
        $kategori_result = query($kategori_sql);
        $kategori_count = fetch_assoc($kategori_result);
        
        echo "<p><strong>Total Kategori:</strong> " . $kategori_count['total'] . "</p>";
        
        if ($kategori_count['total'] > 0) {
            echo "<p class='success'>✅ Tabel kategori ada data</p>";
            
            $list_kategori = "SELECT * FROM kategori ORDER BY nama_kategori";
            $list_result = query($list_kategori);
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Nama Kategori</th></tr>";
            while ($row = fetch_assoc($list_result)) {
                echo "<tr>";
                echo "<td>{$row['id_kategori']}</td>";
                echo "<td>" . htmlspecialchars($row['nama_kategori']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ Tabel kategori kosong!</p>";
            echo "<p><a href='admin/fix_kategori.php' target='_blank'>→ Klik di sini untuk menambah data kategori</a></p>";
        }
        
        echo "<h3>Cek Join Buku-Kategori:</h3>";
        $join_sql = "SELECT 
                        b.id_buku,
                        b.judul,
                        b.id_kategori,
                        k.nama_kategori,
                        k.nama_kategori as kategori
                     FROM buku b
                     LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
                     ORDER BY b.judul
                     LIMIT 5";
        $join_result = query($join_sql);
        
        echo "<table>";
        echo "<tr><th>ID Buku</th><th>Judul</th><th>ID Kategori</th><th>Nama Kategori</th><th>Field 'kategori'</th></tr>";
        
        while ($row = fetch_assoc($join_result)) {
            echo "<tr>";
            echo "<td>{$row['id_buku']}</td>";
            echo "<td>" . htmlspecialchars($row['judul']) . "</td>";
            echo "<td>" . ($row['id_kategori'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['nama_kategori'] ? htmlspecialchars($row['nama_kategori']) : 'NULL') . "</td>";
            echo "<td>" . ($row['kategori'] ? htmlspecialchars($row['kategori']) : 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
    
    <div class="debug-section">
        <h2>3. Android Model Mapping Check</h2>
        <p>Berdasarkan Book model Android yang Anda berikan:</p>
        <ul>
            <li><strong>Field yang diharapkan:</strong> <code>kategori</code></li>
            <li><strong>Method getter:</strong> <code>getKategori()</code></li>
            <li><strong>Method setter:</strong> <code>setKategori(String kategori)</code></li>
        </ul>
        
        <p><strong>Kemungkinan masalah di Android:</strong></p>
        <ol>
            <li><strong>Parsing JSON salah:</strong> Pastikan parsing menggunakan <code>book.setKategori(jsonObject.getString("kategori"))</code></li>
            <li><strong>Field null handling:</strong> Cek apakah ada null check di parsing</li>
            <li><strong>Data tidak di-refresh:</strong> Coba restart aplikasi atau clear cache</li>
        </ol>
    </div>
    
    <div class="debug-section">
        <h2>4. Rekomendasi Perbaikan</h2>
        <ol>
            <li><strong>Jika tabel kategori kosong:</strong> 
                <a href="admin/fix_kategori.php" target="_blank">Jalankan fix_kategori.php</a>
            </li>
            <li><strong>Jika API response tidak ada field 'kategori':</strong> 
                Cek query SQL di api/books.php
            </li>
            <li><strong>Jika field 'kategori' ada tapi kosong:</strong> 
                Update data buku untuk menambah kategori
            </li>
            <li><strong>Jika masalah di Android:</strong> 
                Cek parsing JSON di MainActivity atau BookAdapter
            </li>
        </ol>
    </div>
    
    <p><a href="admin/tambah_buku.php">← Kembali ke Admin</a></p>
</body>
</html>
