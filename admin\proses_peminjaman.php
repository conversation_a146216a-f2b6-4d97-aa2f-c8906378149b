<?php
require_once 'includes/header.php';

// Cek ID peminjaman dan aksi
if (!isset($_GET['id']) || empty($_GET['id']) || !isset($_GET['action']) || empty($_GET['action'])) {
    set_flash_message('danger', 'Parameter tidak valid!');
    redirect('peminjaman.php');
}

$id_peminjaman = clean($_GET['id']);
$action = clean($_GET['action']);

// Ambil data peminjaman
$sql = "SELECT p.*, b.judul, b.jumlah_stok 
        FROM peminjaman p
        JOIN buku b ON p.id_buku = b.id_buku
        WHERE p.id_peminjaman = $id_peminjaman";
$result = query($sql);

if (num_rows($result) !== 1) {
    set_flash_message('danger', 'Peminjaman tidak ditemukan!');
    redirect('peminjaman.php');
}

$peminjaman = fetch_assoc($result);

// Proses berdasarkan aksi
if ($action == 'approve') {
    // Cek status peminjaman
    if ($peminjaman['status_peminjaman'] != 'menunggu') {
        set_flash_message('danger', 'Peminjaman tidak dapat disetujui karena status tidak menunggu!');
        redirect('detail_peminjaman.php?id=' . $id_peminjaman);
    }
    
    // Cek stok buku
    if ($peminjaman['jumlah_stok'] <= 0) {
        set_flash_message('danger', 'Peminjaman tidak dapat disetujui karena stok buku habis!');
        redirect('detail_peminjaman.php?id=' . $id_peminjaman);
    }
    
    // Proses persetujuan
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $catatan = clean($_POST['catatan'] ?? '');
        
        // Update status peminjaman
        $sql_update = "UPDATE peminjaman SET 
                      status_peminjaman = 'dipinjam', 
                      catatan = '$catatan'
                      WHERE id_peminjaman = $id_peminjaman";
        $result_update = query($sql_update);
        
        if ($result_update) {
            // Kurangi stok buku
            $id_buku = $peminjaman['id_buku'];
            $stok_baru = $peminjaman['jumlah_stok'] - 1;
            
            $sql_update_stok = "UPDATE buku SET jumlah_stok = $stok_baru WHERE id_buku = $id_buku";
            query($sql_update_stok);
            
            set_flash_message('success', 'Peminjaman berhasil disetujui!');
            redirect('peminjaman.php');
        } else {
            set_flash_message('danger', 'Gagal menyetujui peminjaman!');
        }
    }
    
    // Tampilkan form persetujuan
    ?>
    <div class="container-fluid">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Setujui Peminjaman</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p>Anda akan menyetujui peminjaman buku <strong><?php echo $peminjaman['judul']; ?></strong>.</p>
                    <p>Stok buku saat ini: <strong><?php echo $peminjaman['jumlah_stok']; ?></strong></p>
                </div>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="catatan" class="form-label">Catatan (opsional)</label>
                        <textarea class="form-control" id="catatan" name="catatan" rows="3"></textarea>
                    </div>
                    <div class="d-flex justify-content-end">
                        <a href="detail_peminjaman.php?id=<?php echo $id_peminjaman; ?>" class="btn btn-secondary me-2">Batal</a>
                        <button type="submit" class="btn btn-success">Setujui Peminjaman</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php
} elseif ($action == 'reject') {
    // Cek status peminjaman
    if ($peminjaman['status_peminjaman'] != 'menunggu') {
        set_flash_message('danger', 'Peminjaman tidak dapat ditolak karena status tidak menunggu!');
        redirect('detail_peminjaman.php?id=' . $id_peminjaman);
    }
    
    // Proses penolakan
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $catatan = clean($_POST['catatan'] ?? '');
        
        // Update status peminjaman
        $sql_update = "UPDATE peminjaman SET 
                      status_peminjaman = 'ditolak', 
                      catatan = '$catatan'
                      WHERE id_peminjaman = $id_peminjaman";
        $result_update = query($sql_update);
        
        if ($result_update) {
            set_flash_message('success', 'Peminjaman berhasil ditolak!');
            redirect('peminjaman.php');
        } else {
            set_flash_message('danger', 'Gagal menolak peminjaman!');
        }
    }
    
    // Tampilkan form penolakan
    ?>
    <div class="container-fluid">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Tolak Peminjaman</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <p>Anda akan menolak peminjaman buku <strong><?php echo $peminjaman['judul']; ?></strong>.</p>
                </div>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="catatan" class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="catatan" name="catatan" rows="3" required></textarea>
                    </div>
                    <div class="d-flex justify-content-end">
                        <a href="detail_peminjaman.php?id=<?php echo $id_peminjaman; ?>" class="btn btn-secondary me-2">Batal</a>
                        <button type="submit" class="btn btn-danger">Tolak Peminjaman</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php
} elseif ($action == 'return') {
    // Cek status peminjaman
    if ($peminjaman['status_peminjaman'] != 'dipinjam' && $peminjaman['status_peminjaman'] != 'terlambat') {
        set_flash_message('danger', 'Peminjaman tidak dapat dikembalikan karena status tidak dipinjam atau terlambat!');
        redirect('detail_peminjaman.php?id=' . $id_peminjaman);
    }
    
    // Proses pengembalian
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $tanggal_pengembalian = date('Y-m-d');
        $catatan = clean($_POST['catatan'] ?? '');
        $denda = 0;
        
        // Hitung denda jika terlambat
        $tanggal_kembali = new DateTime($peminjaman['tanggal_kembali']);
        $tanggal_sekarang = new DateTime($tanggal_pengembalian);
        
        if ($tanggal_sekarang > $tanggal_kembali) {
            $selisih = $tanggal_sekarang->diff($tanggal_kembali);
            $jumlah_hari_terlambat = $selisih->days;
            $denda = $jumlah_hari_terlambat * 5000; // Denda Rp 5.000 per hari
        }
        
        // Update status peminjaman
        $sql_update = "UPDATE peminjaman SET 
                      status_peminjaman = 'dikembalikan', 
                      tanggal_pengembalian = '$tanggal_pengembalian',
                      denda = $denda,
                      catatan = CONCAT(catatan, '\n', '$catatan')
                      WHERE id_peminjaman = $id_peminjaman";
        $result_update = query($sql_update);
        
        if ($result_update) {
            // Tambah stok buku
            $id_buku = $peminjaman['id_buku'];
            $stok_baru = $peminjaman['jumlah_stok'] + 1;
            
            $sql_update_stok = "UPDATE buku SET jumlah_stok = $stok_baru WHERE id_buku = $id_buku";
            query($sql_update_stok);
            
            set_flash_message('success', 'Buku berhasil dikembalikan!');
            redirect('peminjaman.php');
        } else {
            set_flash_message('danger', 'Gagal mengembalikan buku!');
        }
    }
    
    // Hitung denda jika terlambat
    $tanggal_kembali = new DateTime($peminjaman['tanggal_kembali']);
    $tanggal_sekarang = new DateTime();
    $terlambat = false;
    $jumlah_hari_terlambat = 0;
    $denda = 0;
    
    if ($tanggal_sekarang > $tanggal_kembali) {
        $terlambat = true;
        $selisih = $tanggal_sekarang->diff($tanggal_kembali);
        $jumlah_hari_terlambat = $selisih->days;
        $denda = $jumlah_hari_terlambat * 5000; // Denda Rp 5.000 per hari
    }
    
    // Tampilkan form pengembalian
    ?>
    <div class="container-fluid">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Pengembalian Buku</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo $terlambat ? 'warning' : 'info'; ?>">
                    <p>Anda akan memproses pengembalian buku <strong><?php echo $peminjaman['judul']; ?></strong>.</p>
                    <p>Tanggal Pinjam: <strong><?php echo format_tanggal($peminjaman['tanggal_pinjam']); ?></strong></p>
                    <p>Tanggal Kembali: <strong><?php echo format_tanggal($peminjaman['tanggal_kembali']); ?></strong></p>
                    <p>Tanggal Pengembalian: <strong><?php echo format_tanggal(date('Y-m-d')); ?></strong></p>
                    
                    <?php if ($terlambat): ?>
                    <p class="text-danger">Buku terlambat dikembalikan selama <strong><?php echo $jumlah_hari_terlambat; ?> hari</strong>.</p>
                    <p class="text-danger">Denda: <strong>Rp <?php echo number_format($denda, 0, ',', '.'); ?></strong></p>
                    <?php else: ?>
                    <p class="text-success">Buku dikembalikan tepat waktu.</p>
                    <?php endif; ?>
                </div>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="catatan" class="form-label">Catatan (opsional)</label>
                        <textarea class="form-control" id="catatan" name="catatan" rows="3"></textarea>
                    </div>
                    <div class="d-flex justify-content-end">
                        <a href="detail_peminjaman.php?id=<?php echo $id_peminjaman; ?>" class="btn btn-secondary me-2">Batal</a>
                        <button type="submit" class="btn btn-success">Proses Pengembalian</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php
} else {
    set_flash_message('danger', 'Aksi tidak valid!');
    redirect('peminjaman.php');
}

require_once 'includes/footer.php';
?>
