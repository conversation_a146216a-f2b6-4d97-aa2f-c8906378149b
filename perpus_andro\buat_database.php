<?php
// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'perpus_db';

echo "<h1>Pembuatan Database Perpustakaan</h1>";

// Koneksi ke MySQL tanpa database
$conn = mysqli_connect($host, $username, $password);
if (!$conn) {
    die("<p style='color: red;'>Koneksi ke MySQL gagal: " . mysqli_connect_error() . "</p>");
}
echo "<p style='color: green;'>Koneksi ke MySQL berhasil!</p>";

// Hapus database jika sudah ada
$sql_drop_db = "DROP DATABASE IF EXISTS $database";
if (mysqli_query($conn, $sql_drop_db)) {
    echo "<p>Database '$database' lama dihapus!</p>";
} else {
    echo "<p style='color: red;'>Gagal menghapus database lama: " . mysqli_error($conn) . "</p>";
}

// Buat database baru
$sql_create_db = "CREATE DATABASE $database";
if (mysqli_query($conn, $sql_create_db)) {
    echo "<p style='color: green;'>Database '$database' berhasil dibuat!</p>";
} else {
    die("<p style='color: red;'>Gagal membuat database: " . mysqli_error($conn) . "</p>");
}

// Pilih database
mysqli_select_db($conn, $database);
echo "<p style='color: green;'>Database '$database' dipilih!</p>";

// Baca file SQL
$sql_file = file_get_contents('database/perpus.sql');

// Hapus baris DROP DATABASE dan CREATE DATABASE karena sudah dilakukan
$sql_file = preg_replace('/DROP DATABASE.*?;/s', '', $sql_file);
$sql_file = preg_replace('/CREATE DATABASE.*?;/s', '', $sql_file);
$sql_file = preg_replace('/USE.*?;/s', '', $sql_file);

// Pisahkan query
$queries = explode(';', $sql_file);

// Eksekusi setiap query
$success = true;
$error_messages = [];

foreach ($queries as $query) {
    $query = trim($query);
    if (!empty($query)) {
        if (!mysqli_query($conn, $query)) {
            $success = false;
            $error_messages[] = "Error executing query: " . mysqli_error($conn) . " Query: " . substr($query, 0, 100) . "...";
        }
    }
}

if ($success) {
    echo "<p style='color: green;'>Database berhasil dibuat dan data awal berhasil diimpor!</p>";
} else {
    echo "<p style='color: red;'>Terjadi kesalahan saat mengimpor data:</p>";
    echo "<ul>";
    foreach ($error_messages as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
}

// Verifikasi data admin
$sql_admin = "SELECT * FROM admin";
$result_admin = mysqli_query($conn, $sql_admin);

if ($result_admin && mysqli_num_rows($result_admin) > 0) {
    echo "<p style='color: green;'>Data admin berhasil diverifikasi!</p>";
    echo "<h2>Informasi Login</h2>";
    echo "<p>Admin:</p>";
    echo "<ul>";
    echo "<li>Username: <strong>admin</strong></li>";
    echo "<li>Password: <strong>admin123</strong></li>";
    echo "</ul>";
    
    echo "<p>Petugas:</p>";
    echo "<ul>";
    echo "<li>Username: <strong>petugas</strong></li>";
    echo "<li>Password: <strong>petugas123</strong></li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Data admin tidak ditemukan!</p>";
}

// Tutup koneksi
mysqli_close($conn);

echo "<h2>Langkah Selanjutnya</h2>";
echo "<p>Silakan <a href='admin/login.php'>login</a> dengan salah satu akun di atas.</p>";
?>
