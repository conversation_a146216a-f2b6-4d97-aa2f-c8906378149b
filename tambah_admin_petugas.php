<?php
// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'perpus_db';

echo "<h1>Menambahkan Admin dan <PERSON></h1>";

// Koneksi ke MySQL
$conn = mysqli_connect($host, $username, $password);
if (!$conn) {
    die("<p style='color: red;'>Koneksi ke MySQL gagal: " . mysqli_connect_error() . "</p>");
}
echo "<p style='color: green;'>Koneksi ke MySQL berhasil!</p>";

// Cek apakah database sudah ada
$db_exists = mysqli_select_db($conn, $database);
if (!$db_exists) {
    echo "<p>Database '$database' tidak ditemukan. Membuat database baru...</p>";
    
    // Buat database
    $sql_create_db = "CREATE DATABASE IF NOT EXISTS $database";
    if (mysqli_query($conn, $sql_create_db)) {
        echo "<p style='color: green;'>Database '$database' berhasil dibuat!</p>";
    } else {
        die("<p style='color: red;'>Gagal membuat database: " . mysqli_error($conn) . "</p>");
    }
}

// Pilih database
mysqli_select_db($conn, $database);
echo "<p style='color: green;'>Database '$database' dipilih!</p>";

// Cek apakah tabel admin sudah ada
$result = mysqli_query($conn, "SHOW TABLES LIKE 'admin'");
if (mysqli_num_rows($result) == 0) {
    echo "<p>Tabel 'admin' tidak ditemukan. Membuat tabel admin...</p>";
    
    // Buat tabel admin
    $sql_create_table = "CREATE TABLE IF NOT EXISTS admin (
        id_admin INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        nama_admin VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        no_telp VARCHAR(15),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if (mysqli_query($conn, $sql_create_table)) {
        echo "<p style='color: green;'>Tabel 'admin' berhasil dibuat!</p>";
    } else {
        die("<p style='color: red;'>Gagal membuat tabel admin: " . mysqli_error($conn) . "</p>");
    }
}

// Tambahkan admin
$admin_username = 'admin';
$admin_password = 'admin123';
$admin_name = 'Administrator';
$admin_email = '<EMAIL>';

// Hash password
$hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

// Cek apakah username sudah ada
$sql_check = "SELECT * FROM admin WHERE username = '$admin_username'";
$result_check = mysqli_query($conn, $sql_check);

if (mysqli_num_rows($result_check) > 0) {
    echo "<p>Admin dengan username '$admin_username' sudah ada. Mengupdate password...</p>";
    
    // Update password admin
    $sql_update = "UPDATE admin SET password = '$hashed_password' WHERE username = '$admin_username'";
    
    if (mysqli_query($conn, $sql_update)) {
        echo "<p style='color: green;'>Password admin berhasil diupdate!</p>";
    } else {
        echo "<p style='color: red;'>Gagal mengupdate password admin: " . mysqli_error($conn) . "</p>";
    }
} else {
    // Tambah admin baru
    $sql_insert = "INSERT INTO admin (username, password, nama_admin, email) 
                  VALUES ('$admin_username', '$hashed_password', '$admin_name', '$admin_email')";
    
    if (mysqli_query($conn, $sql_insert)) {
        echo "<p style='color: green;'>Admin baru berhasil ditambahkan!</p>";
    } else {
        echo "<p style='color: red;'>Gagal menambahkan admin: " . mysqli_error($conn) . "</p>";
    }
}

// Tambahkan petugas
$petugas_username = 'petugas';
$petugas_password = 'petugas123';
$petugas_name = 'Petugas Perpustakaan';
$petugas_email = '<EMAIL>';

// Hash password
$hashed_password_petugas = password_hash($petugas_password, PASSWORD_DEFAULT);

// Cek apakah username sudah ada
$sql_check_petugas = "SELECT * FROM admin WHERE username = '$petugas_username'";
$result_check_petugas = mysqli_query($conn, $sql_check_petugas);

if (mysqli_num_rows($result_check_petugas) > 0) {
    echo "<p>Petugas dengan username '$petugas_username' sudah ada. Mengupdate password...</p>";
    
    // Update password petugas
    $sql_update_petugas = "UPDATE admin SET password = '$hashed_password_petugas' WHERE username = '$petugas_username'";
    
    if (mysqli_query($conn, $sql_update_petugas)) {
        echo "<p style='color: green;'>Password petugas berhasil diupdate!</p>";
    } else {
        echo "<p style='color: red;'>Gagal mengupdate password petugas: " . mysqli_error($conn) . "</p>";
    }
} else {
    // Tambah petugas baru
    $sql_insert_petugas = "INSERT INTO admin (username, password, nama_admin, email) 
                          VALUES ('$petugas_username', '$hashed_password_petugas', '$petugas_name', '$petugas_email')";
    
    if (mysqli_query($conn, $sql_insert_petugas)) {
        echo "<p style='color: green;'>Petugas baru berhasil ditambahkan!</p>";
    } else {
        echo "<p style='color: red;'>Gagal menambahkan petugas: " . mysqli_error($conn) . "</p>";
    }
}

// Tutup koneksi
mysqli_close($conn);

echo "<h2>Informasi Login</h2>";
echo "<p>Admin:</p>";
echo "<ul>";
echo "<li>Username: <strong>admin</strong></li>";
echo "<li>Password: <strong>admin123</strong></li>";
echo "</ul>";

echo "<p>Petugas:</p>";
echo "<ul>";
echo "<li>Username: <strong>petugas</strong></li>";
echo "<li>Password: <strong>petugas123</strong></li>";
echo "</ul>";

echo "<h2>Langkah Selanjutnya</h2>";
echo "<p>Silakan <a href='admin/login.php'>login</a> dengan salah satu akun di atas.</p>";
?>
