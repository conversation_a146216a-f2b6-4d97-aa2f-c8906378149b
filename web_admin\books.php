<?php
require_once 'config.php';
checkAdminLogin();

// Handle delete
if (isset($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    
    try {
        // Ambil nama file gambar sebelum dihapus
        $stmt = $pdo->prepare("SELECT image FROM books WHERE id = ?");
        $stmt->execute([$id]);
        $book = $stmt->fetch();
        
        // Hapus dari database
        $stmt = $pdo->prepare("DELETE FROM books WHERE id = ?");
        $stmt->execute([$id]);
        
        // Hapus file gambar dari semua folder
        if ($book && $book['image']) {
            deleteImageMultiFolder($book['image']);
        }
        
        $message = 'Buku berhasil dihapus!';
        $message_type = 'success';
    } catch (PDOException $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'danger';
    }
}

// Ambil data buku
try {
    $stmt = $pdo->query("SELECT * FROM books ORDER BY id DESC");
    $books = $stmt->fetchAll();
} catch (PDOException $e) {
    $books = [];
    $message = 'Error mengambil data: ' . $e->getMessage();
    $message_type = 'danger';
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Buku - Admin Perpustakaan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.2rem 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .book-image {
            width: 60px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3 text-white">
                        <h4><i class="fas fa-book"></i> Perpustakaan</h4>
                        <small>Admin Panel</small>
                    </div>
                    <nav class="nav flex-column px-3">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link active" href="books.php">
                            <i class="fas fa-book"></i> Kelola Buku
                        </a>
                        <a class="nav-link" href="add_book.php">
                            <i class="fas fa-plus"></i> Tambah Buku
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users"></i> Kelola User
                        </a>
                        <a class="nav-link" href="borrows.php">
                            <i class="fas fa-exchange-alt"></i> Peminjaman
                        </a>
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-book"></i> Kelola Buku</h2>
                        <a href="add_book.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah Buku
                        </a>
                    </div>
                    
                    <!-- Alert Message -->
                    <?php if (isset($message)): ?>
                        <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
                            <?= $message ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Tabel Buku -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Daftar Buku (<?= count($books) ?> buku)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($books)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Belum ada buku</h5>
                                    <p class="text-muted">Silakan tambah buku baru untuk memulai</p>
                                    <a href="add_book.php" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Tambah Buku Pertama
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Gambar</th>
                                                <th>Judul</th>
                                                <th>Penulis</th>
                                                <th>Kategori</th>
                                                <th>Tahun</th>
                                                <th>Stok</th>
                                                <th>Status</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($books as $book): ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($book['image']): ?>
                                                            <img src="uploads/<?= htmlspecialchars($book['image']) ?>" 
                                                                 alt="<?= htmlspecialchars($book['title']) ?>" 
                                                                 class="book-image"
                                                                 onerror="this.src='https://via.placeholder.com/60x80?text=No+Image'">
                                                        <?php else: ?>
                                                            <div class="book-image bg-light d-flex align-items-center justify-content-center">
                                                                <i class="fas fa-book text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <strong><?= htmlspecialchars($book['title']) ?></strong>
                                                        <?php if ($book['publisher']): ?>
                                                            <br><small class="text-muted"><?= htmlspecialchars($book['publisher']) ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($book['author']) ?></td>
                                                    <td>
                                                        <span class="badge bg-primary"><?= htmlspecialchars($book['category']) ?></span>
                                                    </td>
                                                    <td><?= $book['year'] ?></td>
                                                    <td>
                                                        <span class="badge bg-<?= $book['stock'] > 0 ? 'success' : 'danger' ?>">
                                                            <?= $book['stock'] ?> buku
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($book['stock'] > 0): ?>
                                                            <span class="badge bg-success">Tersedia</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">Habis</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="edit_book.php?id=<?= $book['id'] ?>" 
                                                               class="btn btn-outline-primary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="view_book.php?id=<?= $book['id'] ?>" 
                                                               class="btn btn-outline-info" title="Lihat Detail">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="?delete=<?= $book['id'] ?>" 
                                                               class="btn btn-outline-danger" title="Hapus"
                                                               onclick="return confirm('Yakin ingin menghapus buku ini?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
