<?php
require_once 'includes/header.php';

// Proses hapus buku
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id_buku = clean($_GET['id']);
    
    // Cek apakah buku sedang dipinjam
    $sql_cek = "SELECT * FROM peminjaman WHERE id_buku = $id_buku AND status_peminjaman IN ('menunggu', 'dipinjam')";
    $result_cek = query($sql_cek);
    
    if (num_rows($result_cek) > 0) {
        set_flash_message('danger', 'Buku tidak dapat dihapus karena sedang dipinjam atau dalam proses peminjaman!');
    } else {
        // Hapus buku
        $sql_delete = "DELETE FROM buku WHERE id_buku = $id_buku";
        $result_delete = query($sql_delete);
        
        if ($result_delete) {
            set_flash_message('success', 'Buku berhasil dihapus!');
        } else {
            set_flash_message('danger', 'Gagal menghapus buku!');
        }
    }
    
    redirect('buku.php');
}
?>

<!-- Manajemen Buku Content -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Buku</h6>
            <a href="tambah_buku.php" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Tambah Buku
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Judul</th>
                            <th>Penulis</th>
                            <th>Kategori</th>
                            <th>Stok</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Ambil data buku
                        $sql = "SELECT b.*, k.nama_kategori 
                                FROM buku b
                                LEFT JOIN kategori k ON b.id_kategori = k.id_kategori
                                ORDER BY b.id_buku DESC";
                        $result = query($sql);
                        $buku = fetch_all($result);
                        
                        if (count($buku) > 0) {
                            foreach ($buku as $b) {
                                echo "<tr>
                                        <td>{$b['id_buku']}</td>
                                        <td>{$b['judul']}</td>
                                        <td>{$b['penulis']}</td>
                                        <td>{$b['nama_kategori']}</td>
                                        <td>{$b['jumlah_stok']}</td>
                                        <td>
                                            <a href='detail_buku.php?id={$b['id_buku']}' class='btn btn-sm btn-info'>
                                                <i class='fas fa-eye'></i>
                                            </a>
                                            <a href='edit_buku.php?id={$b['id_buku']}' class='btn btn-sm btn-warning'>
                                                <i class='fas fa-edit'></i>
                                            </a>
                                            <a href='javascript:void(0);' onclick='confirmDelete({$b['id_buku']})' class='btn btn-sm btn-danger'>
                                                <i class='fas fa-trash'></i>
                                            </a>
                                        </td>
                                    </tr>";
                            }
                        } else {
                            echo "<tr><td colspan='6' class='text-center'>Tidak ada data buku</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Script untuk konfirmasi hapus -->
<script>
    function confirmDelete(id) {
        if (confirm('Apakah Anda yakin ingin menghapus buku ini?')) {
            window.location.href = 'buku.php?action=delete&id=' + id;
        }
    }
</script>

<?php require_once 'includes/footer.php'; ?>
